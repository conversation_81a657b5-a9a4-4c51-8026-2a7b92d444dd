import { BASE_LOCATION } from '@/core/constants/general.constant.js'
import { LAYOUT_PERMISSION_LIST } from '@/contexts/layout/constants/layoutPermission.constants.js'

export const ROUTES_WITHOUT_HEADER = []
export const ROUTES_WITHOUT_FOOTER = ['home']

const BASE_LEGACY_ROUTES = `${BASE_LOCATION}home?ea-route`
export const ROUTE_NAMES = {
  NO_AUTH: {
    LOGIN: 'login',
  },
  AUTH: {
    COURSES: {
      CATEGORY: `${BASE_LEGACY_ROUTES}=categories`,
      HOME: `${BASE_LEGACY_ROUTES}=courses`,
      OPINIONS: `${BASE_LEGACY_ROUTES}=nps`,
      ITINERARY: `${BASE_LEGACY_ROUTES}=itinerary`,
      SURVEYS: `${BASE_LEGACY_ROUTES}=survey`,
      ANNOUNCEMENTS: `${BASE_LEGACY_ROUTES}=announcements`,
    },
    USERS: {
      HOME: 'users',
      FORM: 'user-form',
      PROFILE: 'user-profile',
      FILTERS: `${BASE_LEGACY_ROUTES}=filters`,
    },
    HELP: {
      CONTENT: `${BASE_LEGACY_ROUTES}=help`,
      CATEGORY: `${BASE_LEGACY_ROUTES}=help-category`,
    },
    STATS: {
      HOME: `${BASE_LEGACY_ROUTES}=stats`,
      EXCEL_TOOLS: `${BASE_LEGACY_ROUTES}=stats-export`,
      REPORTS: `${BASE_LEGACY_ROUTES}=zip-files`,
    },
    LIBRARY: {
      HOME: `${BASE_LEGACY_ROUTES}=library`,
    },
  },
  SUPER_ADMIN: {
    GENERAL: `${BASE_LEGACY_ROUTES}=menu-general`,
    CATALOGS: { HOME: `${BASE_LEGACY_ROUTES}=catalog` },
    SECTIONS: { HOME: `${BASE_LEGACY_ROUTES}=sections` },
    DEV: `${BASE_LEGACY_ROUTES}=developer`,
    FILTERS: { HOME: `${BASE_LEGACY_ROUTES}=filter-category` },
    SAML: `${BASE_LEGACY_ROUTES}=saml`,
    INTEGRATIONS: `${BASE_LEGACY_ROUTES}=integrations`,
    IMPORT: `${BASE_LEGACY_ROUTES}=import-users`,
    CRON: `${BASE_LEGACY_ROUTES}=cron`,
    PHP: `${BASE_LEGACY_ROUTES}=php-info`,
    LTI: {
      HOME: 'super-admin-lti',
      CONFIG: 'super-admin-lti-form',
    },
  },
  CAMPUS: `${window.location.origin}/campus/`,
  NOT_FOUND: 'not-found',
}

export const ROUTE_DEFAULT_VIEW = {
  [LAYOUT_PERMISSION_LIST.USERS]: ROUTE_NAMES.AUTH.USERS.HOME,
  [LAYOUT_PERMISSION_LIST.SUPER_ADMIN]: ROUTE_NAMES.SUPER_ADMIN.LTI.HOME,
}
