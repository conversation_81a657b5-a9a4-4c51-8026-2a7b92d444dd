import inquirer from 'inquirer'
import path from 'path'
import fs from 'fs'
import { execSync } from 'child_process'
import { clientSelector, environmentSelector } from '../utils/server.utils.js'
import { runNodeCommand } from '../utils/compile.utils.js'
import { addPadStart } from '../utils/number.utils.js'
import { fileURLToPath } from 'node:url'

let outputDir = `../public${import.meta.env.VITE_BASE_URL}`
function validateOptions(client, env) {
  return inquirer.prompt([
    {
      type: 'confirm',
      name: 'validate',
      message: `Quieres hacer la build de ${client} para el entorno ${env} en la carpeta ${import.meta.env.VITE_BASE_URL}?`,
    },
  ])
}

function getCurrentDateTime() {
  const months = [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December',
  ]
  const now = new Date()
  return {
    date: `${months[now.getMonth()]} - ${addPadStart(now.getDate())} - ${now.getFullYear()}`,
    time: `${addPadStart(now.getHours())}:${addPadStart(now.getMinutes())}:${addPadStart(now.getSeconds())}`,
  }
}

function gitCommand(command = '') {
  let value
  try {
    value = execSync(`git ${command}`, { encoding: 'utf8' }).trim()
  } catch (e) {
    value = 'unknown'
  }
  return value
}

function generateBuildVersion() {
  const buildDir = path.join(path.dirname(fileURLToPath(import.meta.url)), `../../../${outputDir}`)
  fs.mkdirSync(buildDir, { recursive: true })
  const versionFilePath = path.join(buildDir, 'version.json')

  try {
    fs.writeFileSync(
      versionFilePath,
      JSON.stringify(
        {
          ...getCurrentDateTime(),
          branch: gitCommand('rev-parse --abbrev-ref HEAD'),
          commit: gitCommand('rev-parse HEAD'),
          tag: gitCommand('describe --tags --abbrev=0').replace('v', ''),
        },
        null,
        2
      )
    )
    console.log('✅  Version file generated')
  } catch (e) {
    console.log('❌  Error generating version file: ', e.message)
  }
}

async function buildProject(client, env) {
  await runNodeCommand(`bun build-favicon ${client}`)
  console.log('✅  Build favicon')
  await runNodeCommand(`bun locales ${client}`)
  console.log('✅  Locales downloaded')
  process.stdout.write('  Building...')
  await runNodeCommand(`cross-env VITE_CLIENT=${client} vite build --mode=${env} --emptyOutDir`, {
    silentMode: true,
    loadingMode: true,
  })
  process.stdout.write('✅  Generating version file...\r')
  generateBuildVersion()
  process.stdout.write('✅  Copy index.html to templates ...\r')
  generateTemplateFile()
  process.stdout.write(`\r✅  Build ${client} in ${outputDir}\n`)
}

function generateTemplateFile() {
  fs.copyFile(`${outputDir}index.html`, './../templates/admin_vue3_app.html', fs.constants.COPYFILE_FICLONE, (err) => {
    if (err) console.log(err)
  })
}

async function configProject() {
  const { result: client } = await clientSelector()
  const { result: env } = await environmentSelector()
  const { validate } = await validateOptions(client, env)

  if (validate) {
    return await buildProject(client, env)
  }
  console.log('❌  Build aborted')
}

configProject().catch(() => console.log('❌  Build error'))
