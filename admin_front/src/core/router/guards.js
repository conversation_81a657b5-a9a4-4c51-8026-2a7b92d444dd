import { useAuth } from '@/contexts/shared/composables/auth.composable.js'
import {
  LAYOUT_PERMISSION_BY_ROLE,
  LAYOUT_PERMISSION_LIST,
} from '@/contexts/layout/constants/layoutPermission.constants.js'
import { ROUTE_DEFAULT_VIEW, ROUTE_NAMES } from '@/core/constants/router.constants.js'

export async function initValidations(to, from, next) {
  if (!$settings.AUTH_ACTIVE || to.meta.isPublic) {
    return next()
  }
  const { isAuth, initUserData } = useAuth()

  if (!isAuth.value) {
    return next(to.meta.requiresAuth ? { name: $settings.PAGES.ENTRY_PAGE } : undefined)
  } else if (!to.meta.requiresAuth) {
    return next({ name: $settings.PAGES.AUTH_DEFAULT })
  }

  const user = initUserData()
  user.setPermissionList(LAYOUT_PERMISSION_BY_ROLE)
  const allowAccess = validateRouteAccess(user, to.meta.viewPermission)
  const firstAllowed = getFirstAllowed(user)
  if (to.meta.viewPermission === ROUTE_NAMES.NOT_FOUND && firstAllowed) {
    return next({ name: firstAllowed })
  }

  return next(!allowAccess ? { name: $settings.PAGES.AUTH_DEFAULT } : undefined)
}

function validateRouteAccess(user, viewPermission = '') {
  if (user.hasFullAccess() || viewPermission === ROUTE_NAMES.NOT_FOUND) {
    return true
  } else if (viewPermission === LAYOUT_PERMISSION_LIST.SUPER_ADMIN) {
    return false
  }
  return user.isGranted(viewPermission)
}

function getFirstAllowed(user) {
  const found = Object.keys(ROUTE_DEFAULT_VIEW).find((key) => {
    return user.isGranted(key)
  })
  return ROUTE_DEFAULT_VIEW[found]
}
