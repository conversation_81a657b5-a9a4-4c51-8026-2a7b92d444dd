import { createApp } from 'vue'
import { initSiteData } from '@/core/configs/site.config.js'
import { addDirectives } from '@/core/plugins/directives.plugin.js'
import { useAuth } from '@/contexts/shared/composables/auth.composable.js'
import RouterPlugin from '@/core/plugins/router.plugin.js'
import StylesPlugin from '@/core/plugins/styles.plugin.js'
import StorePlugin from '@/core/plugins/store.plugin.js'
import IconsPlugin from '@/core/plugins/icons.plugin.js'
import ToastPlugin from '@/core/plugins/toast.plugin.js'
import I18nPlugin from '@/core/plugins/i18n.plugin.js'
import vueDatePicker from '@/core/plugins/datetime.plugin.js'
import App from '@/contexts/shared/views/App.vue'

async function initMockServer() {
  if ($settings?.START_MOCK_SERVER === 'true') {
    await import('@/core/plugins/mocks.plugin.js').then((MocksPlugin) => MocksPlugin.default.install())
  }
}

initSiteData()
initMockServer().then(async () => {
  const app = createApp(App)
  const router = RouterPlugin.install(app)
  StorePlugin.install(app, router)
  IconsPlugin.install(app)
  ToastPlugin.install(app)
  vueDatePicker.install(app)

  await I18nPlugin.install(app)
  await StylesPlugin.install()
  addDirectives(app)

  const { initUserData } = useAuth()
  app.provide('user', initUserData())
  app.mount('#app')
})
