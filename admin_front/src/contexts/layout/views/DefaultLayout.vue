<template>
  <div class="App">
    <main>
      <AppHeader />
      <AppContent />
    </main>
  </div>
</template>
<script setup>
import AppHeader from '@/contexts/layout/components/AppHeader.vue'
import AppContent from '@/contexts/layout/components/AppContent.vue'
import { useLayoutView } from '@/contexts/layout/composables/LayoutView.composable.js'

const { backgroundImage } = useLayoutView()
</script>
<style scoped lang="scss">
.App {
  background-image: v-bind(backgroundImage);
  background-repeat: no-repeat;
  background-size: cover;

  main {
    display: grid;
    grid-template-rows: auto 1fr;
    min-height: 100svh;
    background-color: var(--bg-color-filter);
  }
}
</style>
