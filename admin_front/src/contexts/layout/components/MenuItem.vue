<template>
  <div
    class="MenuItem"
    :class="{ open }"
  >
    <div class="MenuItemTab">
      <div
        class="ItemContent"
        @click="handlingItemClick(item)"
      >
        <Icon
          class="icon"
          :icon="item.icon"
        />
        <span>{{ item.title }}</span>
        <Icon
          v-if="item.subItems.length"
          :icon="open ? 'angle-down' : 'angle-right'"
        />
      </div>
      <div
        class="subItemsContainer"
        :class="{ open }"
      >
        <div
          v-for="subitem in item.subItems"
          :key="subitem.key"
          class="ItemContent"
          @click="handlingItemClick(subitem)"
        >
          <Icon
            class="icon"
            :icon="subitem.icon"
          />
          <span>{{ subitem.title }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { MenuItemModel } from '@/contexts/layout/models/MenuItem.model.js'
import { inject } from 'vue'

const emit = defineEmits(['toggleOpen'])
defineProps({
  item: {
    type: [MenuItemModel, Object],
    default: () => new MenuItemModel(),
  },
  open: {
    type: Boolean,
    default: false,
  },
})

const { openRoute } = inject('LayoutEmits')
function handlingItemClick(item = new MenuItemModel()) {
  emit('toggleOpen', item.id)
  if (!item.subItems.length) openRoute({ name: item.link })
}
</script>

<style scoped lang="scss">
@use '@/contexts/shared/assets/styles/_breakpoints' as breakpoint;
.MenuItem {
  user-select: none;
  position: relative;
  z-index: 1;
  min-height: 3rem;

  .ItemContent {
    width: 100%;
    display: grid;
    grid-template-columns: 1.2rem 1fr 1rem;
    gap: 0.75rem;
    align-items: center;
    padding: 0.75rem;
    cursor: pointer;
    border-radius: 5px 0 0 5px;

    .icon {
      margin: auto;
    }

    &:hover {
      background-color: var(--color-primary-darkest);
    }
  }

  .subItemsContainer {
    max-height: 0;
    overflow: hidden;
    padding-left: 0.3rem;

    .ItemContent {
      background-color: var(--color-neutral-mid-dark);
      border-radius: 0;
      &:hover {
        background-color: var(--color-primary-dark);
      }
    }
  }

  &.open {
    .subItemsContainer {
      max-height: initial;
    }
  }

  @media #{breakpoint.$breakpoint-xl} {
    min-width: 2.75rem;
    .MenuItemTab {
      position: absolute;
      width: 2.5rem;
      white-space: nowrap;
      overflow: hidden;
    }

    .ItemContent {
      &:hover {
        background-color: unset;
      }
    }

    &.open {
      .MenuItemTab {
        width: 250px;

        > .ItemContent {
          border-radius: 0 7px 0 0;
          background-color: var(--color-primary-dark);
        }

        .subItemsContainer {
          width: 100%;
          margin-left: 2.4rem;
        }
      }
    }
  }

  @media #{breakpoint.$breakpoint-sm} {
    .ItemContent {
      width: 100%;
      grid-template-columns: 1.2rem 1fr auto;
    }

    .MenuItemTab {
      position: unset;
      width: 100%;
    }

    &.open {
      .MenuItemTab {
        width: 100%;
        > .ItemContent {
          border-radius: 0;
        }
        .subItemsContainer {
          margin-left: 0;
        }
      }
    }
  }
}
</style>
