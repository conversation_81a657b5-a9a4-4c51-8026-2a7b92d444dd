import { MenuItemModel } from '@/contexts/layout/models/MenuItem.model.js'
import { ROUTE_NAMES } from '@/core/constants/router.constants.js'

export const LAYOUT_MENU_ITEMS = [
  new MenuItemModel({
    id: 1,
    title: 'Gestión de cursos',
    icon: 'university',
    link: '',
    subItems: [
      new MenuItemModel({ id: 11, title: 'Categorías', link: ROUTE_NAMES.AUTH.COURSES.CATEGORY, icon: 'boxes' }),
      new MenuItemModel({
        id: 12,
        title: 'Cursos',
        link: ROUTE_NAMES.AUTH.COURSES.HOME,
        icon: 'university',
      }),
      new MenuItemModel({ id: 13, title: 'Opiniones', link: ROUTE_NAMES.AUTH.COURSES.OPINIONS, icon: 'sms' }),
      new MenuItemModel({
        id: 14,
        title: 'Itinerarios',
        link: ROUTE_NAMES.AUTH.COURSES.ITINERARY,
        icon: 'clipboard-list',
      }),
      new MenuItemModel({ id: 15, title: 'Encuestas', link: ROUTE_NAMES.AUTH.COURSES.SURVEYS, icon: 'poll' }),
      new MenuItemModel({
        id: 16,
        title: 'Convocatorias',
        link: ROUTE_NAMES.AUTH.COURSES.ANNOUNCEMENTS,
        icon: 'calendar-alt',
      }),
    ],
  }),
  new MenuItemModel({
    id: 2,
    title: 'Gestión de ayuda',
    icon: 'question-circle',
    link: '',
    subItems: [
      new MenuItemModel({
        id: 21,
        title: 'Contenido ayuda',
        link: ROUTE_NAMES.AUTH.HELP.CONTENT,
        icon: 'question-circle',
      }),
      new MenuItemModel({
        id: 22,
        title: 'Categoría ayuda',
        link: ROUTE_NAMES.AUTH.HELP.CATEGORY,
        icon: 'question-circle',
      }),
    ],
  }),
  new MenuItemModel({
    id: 3,
    title: 'Gestión de usuarios',
    icon: 'users',
    link: '',
    subItems: [
      new MenuItemModel({ id: 31, title: 'Usuarios', link: ROUTE_NAMES.AUTH.USERS.HOME, icon: 'users' }),
      new MenuItemModel({ id: 32, title: 'Filtros', link: ROUTE_NAMES.AUTH.USERS.FILTERS, icon: 'filter' }),
    ],
  }),
  new MenuItemModel({
    id: 4,
    title: 'Estadísticas',
    icon: 'signal',
    link: '',
    subItems: [
      new MenuItemModel({ id: 41, title: 'Estadísticas', link: ROUTE_NAMES.AUTH.STATS.HOME, icon: 'signal' }),
      new MenuItemModel({
        id: 42,
        title: 'Herramienta Excel',
        link: ROUTE_NAMES.AUTH.STATS.EXCEL_TOOLS,
        icon: 'file-excel',
      }),
      new MenuItemModel({ id: 43, title: 'Informes y diplomas', link: ROUTE_NAMES.AUTH.STATS.REPORTS, icon: 'file' }),
    ],
  }),
  new MenuItemModel({ id: 5, title: 'Library', icon: 'photo-video', link: ROUTE_NAMES.AUTH.LIBRARY.HOME }),
  new MenuItemModel({
    id: 6,
    title: 'SuperAdmin',
    icon: 'shield-alt',
    link: '',
    subItems: [
      new MenuItemModel({ id: 51, title: 'General', link: ROUTE_NAMES.SUPER_ADMIN.GENERAL, icon: 'cog' }),
      new MenuItemModel({ id: 52, title: 'Catálogos', link: ROUTE_NAMES.SUPER_ADMIN.CATALOGS.HOME, icon: 'bars' }),
      new MenuItemModel({ id: 53, title: 'Secciones', link: ROUTE_NAMES.SUPER_ADMIN.SECTIONS.HOME, icon: 'signal' }),
      new MenuItemModel({ id: 54, title: 'Developer', link: ROUTE_NAMES.SUPER_ADMIN.DEV, icon: 'laptop' }),
      new MenuItemModel({ id: 55, title: 'Filtros', link: ROUTE_NAMES.SUPER_ADMIN.FILTERS.HOME, icon: 'filter' }),
      new MenuItemModel({ id: 56, title: 'SAML', link: ROUTE_NAMES.SUPER_ADMIN.SAML, icon: 'shield-alt' }),
      new MenuItemModel({
        id: 57,
        title: 'Integraciones',
        link: ROUTE_NAMES.SUPER_ADMIN.INTEGRATIONS,
        icon: 'shield-alt',
      }),
      new MenuItemModel({ id: 58, title: 'Import users', link: ROUTE_NAMES.SUPER_ADMIN.IMPORT, icon: 'users' }),
      new MenuItemModel({ id: 59, title: 'Cron', link: ROUTE_NAMES.SUPER_ADMIN.CRON, icon: 'clock' }),
      new MenuItemModel({ id: 60, title: 'PHP info', link: ROUTE_NAMES.SUPER_ADMIN.PHP, icon: ['fab', 'php'] }),
      new MenuItemModel({ id: 61, title: 'LTI', link: ROUTE_NAMES.SUPER_ADMIN.LTI.HOME, icon: 'pencil-square' }),
    ],
  }),
  new MenuItemModel({ id: 7, title: 'Ir al campus', icon: 'user', link: ROUTE_NAMES.CAMPUS }),
]
