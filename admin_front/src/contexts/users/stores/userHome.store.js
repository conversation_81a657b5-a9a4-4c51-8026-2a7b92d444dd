import { defineStore } from 'pinia'
import ApiService from '@/core/services/api.service.js'
import { USER_API_ROUTES, USER_ROLE_NAMES } from '@/contexts/users/constants/users.constants.js'
import { reactive, ref } from 'vue'
import { Pagination } from '@/contexts/shared/models/pagination.model.js'
import { toast } from 'vue3-toastify'
import { UserHome } from '@/contexts/users/models/userHome.model.js'
import { UserFilters } from '@/contexts/users/models/userFilters.model.js'
import { INPUT_TYPES } from '@/contexts/shared/constants/input.constants.js'
import { useI18n } from 'vue-i18n'

export const userHomeStore = defineStore('userHomeStore', () => {
  const userListData = reactive({
    data: [],
    isLoading: false,
    pagination: new Pagination(),
  })

  async function loadUserList(payload) {
    userListData.isLoading = true
    const { data, metadata, error } = await ApiService.get(USER_API_ROUTES.HOME.LIST, payload)
    userListData.isLoading = false
    if (error) {
      return toast.error(error.message)
    }
    userListData.data = (data || []).map((user) => new UserHome(user))
    userListData.pagination = new Pagination(metadata || {})
  }

  async function impersonateUser(payload) {
    const { data, error } = await ApiService.post(USER_API_ROUTES.HOME.IMPERSONATE, payload)
    if (error) {
      return toast.error(error.message)
    }
    window.open(data.url, '_self').focus()
  }

  const i18n = useI18n()
  const filterData = ref({
    dynamic: [],
    static: {
      role: new UserFilters({
        id: 1,
        name: 'Role',
        filters: Object.keys(USER_ROLE_NAMES).map((key) => ({ id: key, name: i18n.t(USER_ROLE_NAMES[key]) })),
        type: INPUT_TYPES.SELECT,
        key: 'role',
        value: '',
      }),
      dateFrom: new UserFilters({
        id: 2,
        name: '',
        type: INPUT_TYPES.DATE,
        key: 'start_date',
        value: '',
      }),
      dateTo: new UserFilters({
        id: 3,
        name: '',
        type: INPUT_TYPES.DATE,
        key: 'end_date',
        value: '',
      }),
      isActive: new UserFilters({
        id: 4,
        name: 'is_active',
        type: INPUT_TYPES.BOOLEAN,
        key: 'is_active',
        value: true,
      }),
      search: new UserFilters({
        id: 5,
        name: 'search',
        key: 'search',
        value: '',
      }),
    },
  })
  const clearedFilters = ref([])
  async function loadUserFilters() {
    const { data, error } = await ApiService.post(USER_API_ROUTES.HOME.FILTER_LIST)
    if (!error) {
      filterData.value.dynamic = data.map((filter) => new UserFilters(filter))
      clearedFilters.value = data.map((filter) => new UserFilters(filter))
    }
  }
  async function exportUser(payload) {
    const { error } = await ApiService.post(USER_API_ROUTES.HOME.EXPORT, payload)
    if (error) {
      return toast.error(error.message)
    } else return toast.success(i18n.t('SUCCESS'))
  }
  async function removeUser(id) {
    const parsedUrl = ApiService.setParams(USER_API_ROUTES.HOME.REMOVE, { id })
    const { error } = await ApiService.delete(parsedUrl)
    if (error) {
      return toast.error(error.message)
    }
  }
  async function toggleUserStatus(id) {
    const parsedUrl = ApiService.setParams(USER_API_ROUTES.HOME.TOGGLE_ACTIVE, { id })
    const { data, error } = await ApiService.put(parsedUrl)
    if (error) {
      toast.error(error)
      return undefined
    }
    return data.isActive
  }

  return {
    userListData,
    filterData,
    clearedFilters,
    loadUserList,
    impersonateUser,
    removeUser,
    toggleUserStatus,
    loadUserFilters,
    exportUser,
  }
})
