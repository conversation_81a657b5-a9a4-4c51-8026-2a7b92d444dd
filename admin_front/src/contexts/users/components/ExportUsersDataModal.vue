<template>
  <div class="ExportUsersDataModal">
    <BaseModal @close="emit('close')">
      <div class="formContent">
        <BaseInput
          v-model="innerValue.name"
          :label="$t('MODAL_FILENAME')"
          name="export_filename"
        />
      </div>
      <div class="filterContent">
        <DynamicInput
          v-for="filter in innerValue.filters"
          :key="filter.key"
          v-model="filter.value"
          :item="filter"
        />
      </div>
      <div class="dateFilter">
        <label>{{ $t('FILTER.CREATED_AT_IN') }}:</label>
        <div class="dateInput">
          <BaseInputDate
            v-model="innerValue.from"
            :label="$t('DATE_TIME_TITLE.FROM')"
            :clearable="true"
          />
          <BaseInputDate
            v-model="innerValue.to"
            :label="$t('DATE_TIME_TITLE.TO')"
            :clearable="true"
          />
        </div>
      </div>
      <div class="buttonContainer">
        <BaseButton @click="emit('submit')"><Icon icon="file-excel" /> {{ $t('COURSE.EXPORT.BUTTON') }}</BaseButton>
        <BaseButton @click="emit('reset')"><Icon icon="trash" /> {{ $t('CATALOG.SETTING.CLEAR_FILTERS') }}</BaseButton>
      </div>
    </BaseModal>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { UserDataExport } from '@/contexts/users/models/userDataExport.model.js'
import BaseModal from '@/contexts/shared/components/BaseModal.vue'
import BaseInput from '@/contexts/shared/components/BaseInput.vue'
import BaseInputDate from '@/contexts/shared/components/BaseInputDate.vue'
import DynamicInput from '@/contexts/shared/components/DynamicInput.vue'
import BaseButton from '@/contexts/shared/components/BaseButton.vue'

const emit = defineEmits(['update:modelValue', 'close', 'reset', 'submit'])
const props = defineProps({
  modelValue: {
    type: [UserDataExport, Object],
    default: () => new UserDataExport(),
  },
  disabled: { type: Boolean, default: false },
})
const innerValue = computed({
  get: () => props.modelValue,
  set: (value) => {
    if (props.disabled) {
      return null
    }
    emit('update:modelValue', value)
  },
})
</script>

<style scoped lang="scss">
.ExportUsersDataModal {
  :deep(.modalContent) {
    main {
      padding: 0;
    }
  }
  .dateFilter {
    background-color: var(--color-primary-lightest);
    p {
      margin-top: 0;
      font-weight: bold;
    }

    .dateInput {
      display: grid;
      grid-template-columns: 1fr auto 1fr;
      gap: 1rem;
    }
  }

  .formContent,
  .filterContent,
  .dateFilter {
    padding: 1rem;
  }

  .filterContent {
    display: grid;
    gap: 1rem;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    padding-bottom: 2rem;
  }

  .buttonContainer {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    gap: 1rem;
    padding: 1rem 1rem 0;
  }
}
</style>
