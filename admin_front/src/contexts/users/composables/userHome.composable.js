import { ref, onMounted, reactive, watch } from 'vue'
import { PageTitleModel } from '@/contexts/shared/models/pageTitle.model.js'
import { useI18n } from 'vue-i18n'
import { storeToRefs } from 'pinia'
import { userHomeStore } from '@/contexts/users/stores/userHome.store.js'
import { useRouter } from 'vue-router'
import { UserDataExport } from '@/contexts/users/models/userDataExport.model.js'
import { addPadStart } from '@/core/utils/number.utils.js'
import { UserListFilters } from '@/contexts/users/models/userListFilters.model.js'
import { ROUTE_NAMES } from '@/core/constants/router.constants.js'

export function useUserHomeComposable() {
  const Router = useRouter()
  const { userListData, filterData, clearedFilters } = storeToRefs(userHomeStore())
  const { loadUserList, loadUserFilters, impersonate<PERSON><PERSON>, toggleUserStatus, removeUser, exportUser } = userHomeStore()
  const links = ref([])
  const searchText = ref('')

  watch(searchText, async (newVal) => {
    filterData.value.static.search.value = newVal
    await applyFilter()
  })

  const isLoading = ref(false)
  async function loadUserData() {
    if (isLoading.value) {
      return null
    }
    isLoading.value = true
    await loadUserList(
      new UserListFilters({
        filters: filterData.value.static,
        extraFilters: [...filterData.value.dynamic],
        payload: userListData.value.pagination,
      }).getPayloadData()
    )
    isLoading.value = false
  }
  const reportModalOptions = reactive({
    open: false,
    form: {},
  })
  function openReportModal() {
    const date = new Date()
    reportModalOptions.form = new UserDataExport({
      name: `User Export ${addPadStart(date.getDate())}-${addPadStart(date.getMonth() + 1)}-${date.getFullYear()}`,
      filters: clearedFilters.value,
    })
    resetModalFilters()
    reportModalOptions.open = true
  }
  function resetModalFilters() {
    reportModalOptions.form.filters.forEach((filter) => filter.reset())
  }
  async function applyFilter() {
    userListData.value.pagination.currentPage = 1
    await loadUserData()
  }
  async function resetListFilters() {
    filterData.value.dynamic.forEach((filter) => filter.reset())
    Object.values(filterData.value.static).forEach((filter) => filter.reset())
    searchText.value = ''
  }
  const exportingData = ref(false)
  async function exportUserData() {
    if (exportingData.value) {
      return null
    }
    exportingData.value = true
    await exportUser(reportModalOptions.form.getPayloadData())
    exportingData.value = false
  }

  function openForm(id = null) {
    Router.push({ name: ROUTE_NAMES.AUTH.USERS.FORM, params: { id } }).catch()
  }

  function goToProfile(id = null) {
    if (!id) {
      return null
    }
    Router.push({ name: ROUTE_NAMES.AUTH.USERS.PROFILE, params: { id } }).catch()
  }

  const selectedId = ref(0)
  function toggleRemove(id = 0) {
    selectedId.value = id
  }

  async function toggleUserActive(userObj = {}) {
    if (!userObj?.id || userObj.isUpdating) {
      return null
    }
    userObj.isUpdating = true
    const status = await toggleUserStatus(userObj.id)
    if (status !== undefined) userObj.isActive = status
    userObj.isUpdating = false
  }

  async function handlingRemoveUser() {
    if (selectedId.value) {
      await removeUser(selectedId.value)
      selectedId.value = 0
      await loadUserList()
    }
  }

  const impersonatingUser = ref(false)
  async function impersonate(id = null) {
    if (id === null || impersonatingUser.value) {
      return null
    }
    impersonatingUser.value = true
    await impersonateUser({ user: id })
    impersonatingUser.value = false
  }

  const showFilters = ref(false)
  function toggleFilters() {
    showFilters.value = !showFilters.value
  }

  onMounted(async () => {
    const i18n = useI18n()
    links.value = [new PageTitleModel({ id: 1, title: i18n.t('USER.LABEL_IN_PLURAL') })]
    await loadUserFilters()
    await loadUserData()
  })

  return {
    searchText,
    links,
    filterData,
    reportModalOptions,
    showFilters,
    userListData,
    selectedId,
    isLoading,
    openReportModal,
    openForm,
    goToProfile,
    toggleUserActive,
    toggleRemove,
    impersonate,
    toggleFilters,
    handlingRemoveUser,
    resetModalFilters,
    exportUserData,
    loadUserData,
    applyFilter,
    resetListFilters,
  }
}
