import { DynamicInputModel } from '@/contexts/shared/models/dynamicInput.model.js'

export class UserFormExtraFieldModel extends DynamicInputModel {
  constructor({ id = 0, key = '', label = '', value = '', required = false } = {}) {
    super({ id, name: label, value })
    this.key = key || this.key
    this.required = required || false
  }

  getInputValue() {
    return { key: this.key, value: this.getParsedDataValue(this.value) }
  }
}
