import { INPUT_TYPES } from '@/contexts/shared/constants/input.constants.js'

export class UserListFilters {
  constructor({ filters = {}, extraFilters = [], payload = {} } = {}) {
    this.filters = filters || {}
    this.extraFilters = extraFilters || []
    this.payload = payload || {}
  }

  getFilterData() {
    return Object.values(this.filters)
      .map((filter) => filter.getInputValue())
      .filter((filter) => (filter.key !== 'is_active' ? !!filter.value : true))
      .reduce((acc, cur) => ({ ...acc, [cur.key]: cur.value }), {})
  }

  getPayloadData() {
    const data = { ...this.getFilterData(), ...this.payload.getPayloadData() }
    this.extraFilters.forEach((filter) => {
      const payload = filter.getInputValue()
      if (filter.type === INPUT_TYPES.BOOLEAN || !!payload.value) {
        if (!data.filters) data.filters = []
        data.filters.push(payload)
      }
    })
    return data
  }
}
