import Joi from 'joi'

export class UserFormModel {
  constructor({
    id = 0,
    name = '',
    last_name = '',
    email = '',
    avatar = '',
    language = '',
    code = '',
    company = '',
    zone = '',
    dni = '',
    open_campus = true,
    extra = [],
    roles = [],
  } = {}) {
    this.id = id || 0
    this.firstName = name || ''
    this.lastName = last_name || ''
    this.fullName = `${this.firstName} ${this.lastName}`.trim()
    this.email = email || ''
    this.avatar = avatar || ''
    this.language = language || ''
    this.code = code || ''
    this.company = company || 0
    this.zone = zone || ''
    this.dni = dni || ''
    this.openCampus = open_campus || false
    this.extraData = extra || []
    this.extra = []
    this.roles = (roles || []).map((role) => role.id)
    this.password = ''
    this.setErrors()
  }

  setErrors(errors = {}) {
    this.errors = errors || {}
  }

  getPayload() {
    return {
      name: this.firstName,
      last_name: this.lastName,
      email: this.email,
      language: this.language,
      code: this.code,
      company: this.company,
      zone: this.zone,
      dni: this.dni,
      open_campus: this.openCampus,
      extra: this.extra.map((field) => ({ key: field.key, value: field.value })),
      roles: this.roles,
      ...(this.password.length ? { password: this.password } : {}),
    }
  }

  updateExtraFields(extraFields = []) {
    this.extra = extraFields.map((field) => {
      const input = this.extraData.find((input) => input.key === field.key)
      if (input) field.value = input.value || ''
      else field.value = ''
      return field
    })
  }

  getValidationSchema() {
    return Joi.object({
      name: Joi.string().required().messages({
        'string.empty': 'REQUIRED_FIELD',
      }),
      last_name: Joi.string().required().messages({
        'string.empty': 'REQUIRED_FIELD',
      }),
      email: Joi.string()
        .email({ tlds: { allow: false } })
        .required()
        .messages({
          'string.email': 'EMAIL.INVALID',
          'string.empty': 'REQUIRED_FIELD',
        }),
      language: Joi.string().required().messages({
        'string.empty': 'REQUIRED_FIELD',
      }),
      code: Joi.string().required().messages({
        'string.empty': 'REQUIRED_FIELD',
      }),
      company: Joi.number().min(1).required().messages({
        'number.min': 'REQUIRED_FIELD',
      }),
      zone: Joi.string().required().messages({
        'string.empty': 'REQUIRED_FIELD',
      }),
      dni: Joi.string().required().messages({
        'string.empty': 'REQUIRED_FIELD',
      }),
      roles: Joi.array().min(1).messages({
        'array.min': 'REQUIRED_FIELD',
      }),
      password: this.id
        ? Joi.string().optional()
        : Joi.string().required().messages({
            'any.required': 'REQUIRED_FIELD',
          }),
      extra: Joi.array(),
      open_campus: Joi.boolean(),
    })
  }
}
