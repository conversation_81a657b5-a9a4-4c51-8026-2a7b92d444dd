import { reactive } from 'vue'
import { useAuthStore } from '@/contexts/shared/stores/auth.store.js'
import { hasErrors } from '@/contexts/shared/utils/validator.utils.js'
import { loginSchema } from '@/contexts/shared/schemas/auth.schema.js'
import { LoaderService } from '@/core/services/loader.service.js'
import { useAuth } from '@/contexts/shared/composables/auth.composable.js'
import { PageTitleModel } from '@/contexts/shared/models/pageTitle.model.js'

export function useLoginView() {
  const links = [new PageTitleModel({ id: 1, title: 'Login' })]
  const logo = LoaderService.importImage(`images/logo`)
  const loginData = reactive({
    payload: {
      email: '',
      password: '',
    },
    errors: {},
    loading: false,
  })
  async function login() {
    const { login } = useAuthStore()
    if (loginData.loading) {
      return null
    }
    loginData.errors = {}
    loginData.loading = true

    const errors = hasErrors(loginData.payload, loginSchema)
    if (errors) {
      errors.forEach((err) => (loginData.errors[err.errorKey] = err.message))
      loginData.loading = false
      return null
    }

    await login(loginData.payload)
    const { isAuth } = useAuth()
    if (isAuth.value) window.location.reload()
    loginData.loading = false
  }

  // TODO: Delete when all contexts have been completed, in the meantime the ‘admin legacy’ login will be used.
  if (!$settings?.START_MOCK_SERVER) {
    return (window.location.href = `${window.location.origin}/admin`)
  }

  return {
    logo,
    login,
    links,
    loginData,
  }
}
