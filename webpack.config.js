const NodePolyfillPlugin = require("node-polyfill-webpack-plugin");
const Encore = require("@symfony/webpack-encore");
const fs = require("fs");
const MakeAfterWebpackPlugin = require('./MakeAfterWebpackPlugin');

const MiniCssExtractPlugin = require("mini-css-extract-plugin");

Encore.addPlugin(
  new MiniCssExtractPlugin({
    filename: "[name].css",
    chunkFilename: "[id].css",
  })
);

if (!Encore.isRuntimeEnvironmentConfigured()) {
  Encore.configureRuntimeEnvironment(process.env.NODE_ENV || "dev");
}

Encore
  .setOutputPath("public/build/")
  .setPublicPath("/build")


  .addEntry("accumulativeStats", "./assets/vue/admin/accumulativeStats.js")
  .addEntry("accessLevel", "./assets/vue/admin/accessLevel.js")
  .addEntry("adivinaImagen", "./assets/vue/admin/adivinaImagen.js")
  .addEntry("admin-max-question", "./assets/js/admin-max-question.js")
  .addEntry("adminDelete", "./assets/js/adminDelete.js")
  .addEntry("app", "./assets/js/app.js")
  .addEntry("categorized", "./assets/vue/admin/categorize.js")
  .addEntry("challenge-detail", "./assets/vue/admin/challenge-detail.js")
  .addEntry("chapterType", "./assets/js/chapterType.js")
  .addEntry("contentPlayer", "./assets/vue/admin/contentPlayer.js")
  .addEntry("courseChapters", "./assets/js/courseChapters.js")
  .addEntry("courseClone", "./assets/js/course-clone.js")
  .addEntry("courseExport", "./assets/js/courseExport.js")
  .addEntry("diploma", "./assets/js/diploma.js")
  .addEntry("diplomaDhl", "./assets/js/diplomaDhl.js")
  .addEntry("diplomaNovomatic", "./assets/js/diplomaNovomatic.js")
  .addEntry("documentation", "./assets/js/documentation.js")
  .addEntry("emailing", "./assets/vue/admin/emailing.js")
  .addEntry("emailingDetail", "./assets/vue/admin/emailingDetail.js")
  .addEntry("fillgaps", "./assets/vue/admin/fillgaps.js")
  .addEntry("guessword", "./assets/vue/admin/guessword.js")
  .addEntry("handleFormSubmitFile", "./assets/js/handleFormSubmitFile.js")
  .addEntry("iframeVimeo", "./assets/vue/admin/iframeVimeo.js")
  .addEntry("lettersoup", "./assets/vue/admin/lettersoup.js")
  .addEntry("list-question-games", "./assets/vue/admin/listQuestionGames.js")
  .addEntry("maintenance", "./assets/js/maintenance.js")
  .addEntry("managerEdit", "./assets/vue/admin/managerEdit.js")
  .addEntry("open_visible", "./assets/js/open_visible.js")
  .addEntry("ordenarMenorMayor", "./assets/vue/admin/ordenarMenorMayor.js")
  .addEntry("parejas", "./assets/vue/admin/parejas.js")
  .addEntry("pdf", "./assets/js/pdf.js")
  .addEntry("player", "./assets/js/player.js")
  .addEntry("material-course", "./assets/vue/admin/MaterialCourse.js")
  .addEntry("task-course", "./assets/vue/admin/TaskCourse.js")
  .addEntry("rouletteWords", "./assets/vue/admin/rouletteWords.js")
  .addEntry("scorm", "./assets/js/scorm.js")
  .addEntry("scormDetail", "./assets/js/scormDetail.js")
  .addEntry("scormPackage", "./assets/vue/admin/scormPackage.js")
  .addEntry("settings", "./assets/vue/admin/settings.js")
  .addEntry("statsExport", "./assets/js/statsExport.js")
  .addEntry("summon", "./assets/vue/admin/summon.js")
  .addEntry("translateCourse", "./assets/js/translateCourse.js")
  .addEntry("trueOrFalse", "./assets/vue/admin/trueOrFalse.js")
  .addEntry("uploadPuzzle", "./assets/vue/admin/uploadPuzzle.js")
  .addEntry("uploadVideo", "./assets/vue/admin/uploadVideo.js")
  .addEntry("user", "./assets/vue/admin/user.js")
  .addEntry("user-impersonate", "./assets/js/user-impersonate.js")
  .addEntry("videoquiz", "./assets/vue/admin/videoquiz.js")
  .addEntry("visorPdf", "./assets/vue/admin/visorPdf.js")
  .addEntry("visorPpt", "./assets/vue/admin/visorPpt.js")
  .addEntry("visorResources", "./assets/vue/admin/visorResources.js")
  .addEntry("wordle", "./assets/vue/admin/wordle.js")
  .addEntry('saveLtiChapter', './assets/vue/admin/saveLtiChapter.js')

  //stats
  .addEntry("generalStats", "./assets/vue/admin/generalStats.js")
  .addEntry("itinerary", "./assets/vue/admin/itinerary.js")
  .addEntry("segmentedStats", "./assets/vue/admin/segmentedStats.js")
  .addEntry("newStats", "./assets/vue/admin/stats/index.js")
  .addEntry("userDetail", "./assets/vue/admin/userDetail.js")
  .addEntry("courseStats", "./assets/vue/admin/course/courseStatsMain.js")

  // Considered as mini-apps
  .addEntry("admin_integrations_app", "./assets/vue/integrations/main.js")
  .addEntry("announcement_app", "./assets/vue/announcement/main.js")
  .addEntry("categoryFilter_app", "./assets/vue/categoryFilter/main.js")
  .addEntry("category_app", "./assets/vue/courseCategory/main.js")
  .addEntry("chapter_type_app", "./assets/vue/chapter/main.js")
  .addEntry("company_app", "./assets/vue/company/main.js")
  .addEntry("course_app", "./assets/vue/course/main.js")
  .addEntry("course_category_app", "./assets/vue/courseCategory/main.js")
  .addEntry("help_category_app", "./assets/vue/helpCategory/main.js")
  .addEntry("developer_app", "./assets/vue/developer/main.js")
  .addEntry("documentation_app", "./assets/vue/documentation/main.js")
  .addEntry("help_text_app", "./assets/vue/helpText/main.js")
  .addEntry("inspector_fundae_app", "./assets/vue/inspectorFundae/main.js")
  .addEntry("itinerary_app", "./assets/vue/itinerary/main.js")
  .addEntry("library_app", "./assets/vue/library/main.js")
  .addEntry("news_app", "./assets/vue/news/main.js")
  .addEntry("opinion_app", "./assets/vue/opinions/main.js")
  .addEntry("pages_app", "./assets/vue/pages/main.js")
  .addEntry("section_app", "./assets/vue/section/main.js")
  .addEntry("setting_catalog_app", "./assets/vue/settings_catalog/main.js")
  .addEntry("settings_saml_app", "./assets/vue/settings_saml/main.js")
  .addEntry("survey_app", "./assets/vue/survey/main.js")
  .addEntry("courses_new_app", "./assets/vue/courses/main.js")
  .addEntry("zipApp", "./assets/vue/zip/main.js")
  .addEntry("users_new_app", "./assets/vue/users/main.js")


  .splitEntryChunks()

  .disableSingleRuntimeChunk()

  .cleanupOutputBeforeBuild()
  .enableBuildNotifications()
  .enableSourceMaps(!Encore.isProduction())
  .enableVersioning(Encore.isProduction())

  .configureBabelPresetEnv((config) => {
    config.useBuiltIns = "usage";
    config.corejs = 3;
  })

  .enableVueLoader()
  .addPlugin(new NodePolyfillPlugin())
  .addPlugin(new MakeAfterWebpackPlugin());

function copyImages(folder, files, client) {
  for (let file of files) {
    let filePath = `./assets/clients/${client}/${folder}/${file}`;
    let pattern = new RegExp(
      file.replace(/[/\-\\^$*+?.()|[\]{}]/g, "\\$&"),
      "i"
    );

    if (client && fs.existsSync(filePath)) {
      Encore.copyFiles({
        from: `./assets/clients/${client}/${folder}`,
        to: `../assets/${folder}/[name].[ext]`,
        pattern: pattern,
      });
    } else {
      Encore.copyFiles({
        from: `./assets/images/${folder}`,
        to: `../assets/${folder}/[name].[ext]`,
        pattern: pattern,
      });
    }
  }
}

function copyFavicon(client) {
  const fs = require("fs");
  const favicon = "favicon.ico";

  if (client && fs.existsSync(`./assets/clients/${client}/${favicon}`)) {
    Encore.copyFiles({
      from: `./assets/clients/${client}`,
      to: `../${favicon}`,
      pattern: new RegExp(favicon, "i"),
    });
  } else {
    Encore.copyFiles({
      from: "./assets/images",
      to: `../${favicon}`,
      pattern: new RegExp(favicon, "i"),
    });
  }
}

module.exports = (env) => {
  const client =
    env?.client && env.client !== "easylearning" ? env.client : null;

  Encore.enableSassLoader((options) => {
    let additionalData = `@import "./assets/css/config/_variables.scss";`;

    if (client) {
      additionalData += `@import "./assets/clients/${client}/_variables.scss";`;
    }

    additionalData += `@import "./assets/css/config/global.scss";`;
    options.sassOptions = {
        quietDeps: true
    }
    options.additionalData = additionalData;
  });

  const emailFiles = ["logo.png"];
  const diplomaFiles = [
    "footer_diploma.svg",
    "logo_diploma.png",
    "logo_diploma.svg",
    "signature.png",
  ];
  const loginFiles = ["logo.png"];

  copyImages("login", emailFiles, client);
  copyImages("email", emailFiles, client);
  copyImages("diploma/easylearning", diplomaFiles, client);
  copyImages("login", loginFiles, client);

  copyFavicon(client);

  Encore.configureDefinePlugin((options) => {
    options["process.env.CLIENT"] = JSON.stringify(client);
  });

  const config = Encore.getWebpackConfig()

    config.ignoreWarnings = [
        warning => {
            return typeof warning === 'object' && warning.details.includes('node_modules');
        }
    ];

    config.plugins.forEach(plugin => {
        if (plugin.constructor.name === 'MiniCssExtractPlugin') {
            plugin.options.ignoreOrder = true;
        }
    });

  return config;
};
