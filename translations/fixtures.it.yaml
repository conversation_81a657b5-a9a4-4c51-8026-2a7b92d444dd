nps_question.text.description: 'Dateci la vostra opinione'
nps_question.nps.description: 'Valutazione del corso'
type_course.teleformacion.name: E-learning
type_course.teleformacion.description: 'Per i corsi di e-learning'
type_course.presencial.name: 'Di persona'
type_course.presencial.description: 'Corsi in loco'
type_course.mixto.name: Misto
type_course.mixto.description: 'Si tratta di una combinazione di e-learning e formazione faccia a faccia'
type_course.aula_virtual.name: 'Aula virtuale'
type_course.aula_virtual.description: 'Le lezioni si tengono in videoconferenza'
alert_type_tutor.1.name: 'La persona chiamata non ha avuto accesso al corso'
alert_type_tutor.1.description: 'Se la persona convocata non accede al corso, viene inviato un avviso al tutor'
alert_type_tutor.2.name: 'Il 50% della chiamata è trascorso e il 25% del contenuto non è stato completato'
alert_type_tutor.2.description: 'Un avviso verrà inviato al tutor se è trascorso il 50% della chiamata e se il 25% del contenuto non è stato completato'
alert_type_tutor.3.name: 'L''80% della chiamata è trascorso e il 50% del contenuto non è stato completato'
alert_type_tutor.3.description: 'Se l''80% della chiamata è trascorso e il 50% del contenuto non è stato completato, viene inviato un avviso al tutor'
alert_type_tutor.4.name: 'Mancano pochi giorni alla fine dell''appello e il corso non è ancora stato completato'
alert_type_tutor.4.description: 'È necessario valutare il numero di giorni che sono considerati pochi'
alert_type_tutor.5.name: 'La persona chiamata ha completato il corso ma non ha risposto al sondaggio.'
alert_type_tutor.5.description: 'Se la piattaforma prevede dei sondaggi, verrà inviato un avviso al tutor se la persona chiamata ha completato il corso ma non ha risposto al sondaggio'
alert_type_tutor.6.name: 'La persona ha completato il corso ma non ha scaricato il diploma'
alert_type_tutor.6.description: 'Il tutor riceverà un avviso se la persona convocata ha completato il corso ma non ha scaricato il diploma.'
announcement_configuration_type.temporalizacion.name: Tempistica
announcement_configuration_type.temporalizacion.description: 'Per facilitare il monitoraggio del corso, assegneremo un tempo a ogni blocco di contenuti e attività, potendo così rilevare quali partecipanti stanno lavorando a un ritmo adeguato o sono in ritardo nel processo di formazione'
announcement_configuration_type.curso_bonificado.name: 'Corso sovvenzionato'
announcement_configuration_type.curso_bonificado.description: 'I corsi sovvenzionati sono quelli realizzati attraverso la Fondazione Tripartita e finanziati dalle aziende attraverso i contributi sociali.'
announcement_configuration_type.chat.name: Chat
announcement_configuration_type.chat.description: 'La chat è uno strumento di comunicazione sincrona che consente ai partecipanti al corso di interagire in tempo reale, tramite messaggi di testo.'
announcement_configuration_type.notificaciones.name: Notifiche
announcement_configuration_type.notificaciones.description: 'Le notifiche sono messaggi inviati ai partecipanti al corso per informarli di notizie o eventi importanti.'
announcement_configuration_type.mensajeria.name: Messaggistica
announcement_configuration_type.mensajeria.description: 'Un sistema di messaggistica è un sistema di comunicazione che consente ai partecipanti al corso di inviare e ricevere messaggi privati.'
announcement_configuration_type.foros.name: Forum
announcement_configuration_type.foros.description: 'Un forum è uno spazio di comunicazione asincrono che consente ai partecipanti al corso di scambiarsi messaggi su un determinato argomento.'
announcement_configuration_type.diploma.name: Diploma
announcement_configuration_type.diploma.description: 'I diplomi sono certificati che vengono rilasciati ai partecipanti di un corso come prova di completamento.'
announcement_configuration_type.tutor_alerts.name: 'Attivare gli avvisi del tutor'
announcement_configuration_type.tutor_alerts.description: 'Gli avvisi sono messaggi inviati al tutor di un corso per informarlo di notizie o eventi importanti.'
announcement_configuration_type.encuesta_satisfaccion.name: 'Sondaggio di soddisfazione'
announcement_configuration_type.encuesta_satisfaccion.description: 'I sondaggi di soddisfazione sono questionari inviati ai partecipanti al corso per conoscere la loro opinione sul corso.'
announcement_configuration_type.finalizar_convocatoria.name: 'Il corso rimarrà attivo al termine della chiamata'
announcement_configuration_type.finalizar_convocatoria.description: 'L''utente potrà accedere ai contenuti del corso anche dopo la sua conclusione.'
announcement_configuration_type.firma_digital.name: 'Firma digitale'
announcement_configuration_type.firma_digital.description: 'Per poter firmare la partecipazione a un corso frontale è necessaria la firma digitale.'
announcement_configuration_type.gestion_costes.name: 'Gestione dei costi'
announcement_configuration_type.gestion_costes.description: 'La gestione dei costi consente ai gruppi di indicare il costo della chiamata.'
announcement_configuration_type.EMAIL_NOTIFICATION_ON_ANNOUNCEMENT.name: NOTIFICA_E-MAIL_SU_ANNUNCIO
announcement_configuration_type.EMAIL_NOTIFICATION_ON_ANNOUNCEMENT.description: 'Abilitare le notifiche di posta elettronica.'
announcement_configuration_type.NOTIFICATION_ON_ANNOUNCEMENT.name: NOTIFICA_SU_ANNUNCIO
announcement_configuration_type.NOTIFICATION_ON_ANNOUNCEMENT.description: 'Abilita le notifiche normali'
announcement_configuration_type.objetivos_contenidos.name: 'Obiettivi e contenuti'
announcement_configuration_type.objetivos_contenidos.description: 'Gli obiettivi e i contenuti del corso saranno inclusi nel diploma.'
announcement_configuration_type.dni.name: Dni
announcement_configuration_type.dni.description: 'L''ID dello studente sarà riportato sul diploma'
announcement_configuration_type.template_excel.name: 'Modello di registrazione excel'
announcement_configuration_type.template_excel.description: 'Questo modello verrà utilizzato per l''iscrizione degli studenti, utilizzando il codice HRBP invece del DNI.'
announcement_configuration_type.report_zip.name: ZIP
announcement_configuration_type.report_zip.description: 'Consentire al tutor di scaricare i rapporti di gruppo in formato ZIP'
announcement_criteria.1.name: 'Capitoli completi'
announcement_criteria.1.description: '% di capitoli richiesti per contribuire al criterio di superamento del bando.'
announcement_criteria.2.name: 'Completamento dei compiti'
announcement_criteria.2.description: '% di compiti richiesti per contribuire al criterio di superamento del bando.'
announcement_criteria.3.name: 'Massimo tempo di inattività'
announcement_criteria.3.description: 'Una volta raggiunto questo numero di minuti di tempo trascorso sullo schermo senza inattività, il conteggio del tempo verrà messo in pausa finché non verrà rilevata nuovamente un''attività dell''utente.'
announcement_criteria.4.name: 'Completare le attività'
announcement_criteria.4.description: 'Una volta raggiunto questo numero di minuti di tempo trascorso sullo schermo senza inattività, il conteggio del tempo verrà messo in pausa finché non verrà rilevata nuovamente un''attività dell''utente.'
announcement_criteria.5.name: 'Tempo completato'
announcement_criteria.5.description: '% di ore necessarie per contribuire al superamento del criterio di selezione.'
announcement_step_creation.ANNOUNCEMENT_COURSE.description: 'Primo passo nella creazione della chiamata'
announcement_step_creation.ANNOUNCEMENT_GENERAL_INFO.description: 'Seconda fase della creazione della chiamata, in cui vengono inserite le informazioni generali'
announcement_step_creation.ANNOUNCEMENT_BONUS.description: 'Questa fase dipende dall''attivazione del bonus da parte del cliente'
announcement_step_creation.ANNOUNCEMENT_STUDENTS.description: 'È qui che vengono aggiunti alla chiamata gli studenti, che possono essere assegnati a un gruppo'
announcement_step_creation.ANNOUNCEMENT_GROUPS.description: 'In questo passaggio vengono configurati i gruppi di studenti creati nel passaggio precedente'
announcement_step_creation.ANNOUNCEMENT_COMMUNICATION.description: 'In questa fase si configura il sondaggio da inviare agli studenti'
announcement_step_creation.ANNOUNCEMENT_SURVEY.description: 'In questa fase si configura il sondaggio da inviare agli studenti'
announcement_step_creation.ANNOUNCEMENT_CERTIFICATE.description: 'In questa fase, i diplomi disponibili sulla piattaforma vengono visualizzati affinché il cliente possa selezionare quello desiderato'
announcement_step_creation.ANNOUNCEMENT_ALERTS.description: 'Questi avvisi sono avvisi speciali che informano il tutor sugli eventi della chiamata'
class_room_virtual.zoom.name: Zoom
class_room_virtual.zoom.description: 'Piattaforma per conferenze web online, che consente videochiamate ad alta definizione, con funzionalità di condivisione del desktop, lavagna, chat, registrazione di conferenze, condivisione di documenti e accesso da qualsiasi luogo, in quanto disponibile per dispositivi mobili.'
class_room_virtual.clickmeeting.name: ClickMeeting
class_room_virtual.clickmeeting.description: 'La piattaforma di ClickMeeting è una delle interfacce per webinar più facili da usare sul mercato e offre molte opzioni di personalizzazione flessibili'
class_room_virtual.jitsi.name: Jitsi
class_room_virtual.jitsi.description: 'Soluzione open source per videoconferenze con connessioni criptate e disponibile per diversi sistemi operativi'
class_room_virtual.plugnmeet.name: PlugnMeet
class_room_virtual.plugnmeet.description: 'Software per videoconferenze open source facile da integrare e altamente personalizzabile'
configuration_cliente_announcement.COMMUNICATION.description: 'Ciò consente di attivare le comunicazioni all''interno della chiamata'
configuration_cliente_announcement.CERTIFICATE.description: 'Ciò consente di scaricare i diplomi all''interno della chiamata'
configuration_cliente_announcement.SURVEY.description: 'In questo modo è possibile attivare i sondaggi e renderli disponibili in un secondo momento durante la chiamata'
configuration_cliente_announcement.ALERT.description: 'Questo permette di abilitare gli avvisi, che saranno ereditati nella sezione avvisi del tutor'
configuration_cliente_announcement.TEMPORALIZATION.description: 'Consente la temporalizzazione dei capitoli all''interno di un invito a presentare proposte'
configuration_cliente_announcement.BONIFICATION.description: 'Bonus per l''appello, in particolare per l''appello della fondazione tripartita'
configuration_cliente_announcement.ACCESS_CONTENT.description: 'Consente di accedere al contenuto della chiamata una volta terminata'
configuration_cliente_announcement.DIGITAL_SIGNATURE.description: 'Consente di abilitare la firma digitale nel bando, soprattutto nei corsi frontali'
configuration_cliente_announcement.COST.description: 'Consentire ai clienti di attribuire i costi alla chiamata.'
configuration_cliente_announcement.NOTIFICATION_ACTIVATE_ANNOUNCEMENT.description: 'Notifiche sull''attivazione della chiamata (e-mail, notifica frontale)'
configuration_cliente_announcement.CONFIGURATION_IBEROSTAR.description: 'Questa configurazione è stata studiata appositamente per i clienti Iberostar, in modo da non influenzare il flusso di fondi'
configuration_cliente_announcement.REPORT.description: 'Consentire la rendicontazione negli inviti a presentare proposte'
type_course_announcement_step_creation.seleccionar_curso.name: 'Selezionare il corso'
type_course_announcement_step_creation.seleccionar_curso.description: 'Selezionare il corso per il quale deve essere creato l''appello'
type_course_announcement_step_creation.convocatoria.name: 'Invito a presentare candidature'
type_course_announcement_step_creation.convocatoria.description: 'Informazioni sull''invito a presentare proposte'
type_course_announcement_step_creation.bonificacion.name: Bonus
type_course_announcement_step_creation.bonificacion.description: 'Informazioni sull''invito a presentare proposte'
type_course_announcement_step_creation.alumnado.name: Alumni
type_course_announcement_step_creation.alumnado.description: 'Gli studenti vengono aggiunti al corso'
type_course_announcement_step_creation.grupos.name: Gruppi
type_course_announcement_step_creation.grupos.description: 'Le informazioni sul gruppo sono dettagliate e viene aggiunto anche il tutor'
type_course_announcement_step_creation.comunicacion.name: Comunicazione
type_course_announcement_step_creation.comunicacion.description: 'Questa fase dipende dall''attivazione o meno della comunicazione da parte del cliente.'
type_course_announcement_step_creation.encuesta.name: Sondaggio
type_course_announcement_step_creation.encuesta.description: 'Questa fase dipende dal fatto che il cliente abbia attivato o meno l''indagine.'
type_course_announcement_step_creation.diploma.name: Diploma
type_course_announcement_step_creation.diploma.description: 'Questo passaggio dipende dal fatto che il cliente abbia attivato o meno il diploma.'
type_course_announcement_step_creation.alertas.name: Avvisi
type_course_announcement_step_creation.alertas.description: 'Ciò può dipendere dalla configurazione del cliente.'
type_diploma.easylearning.name: Predefinito
type_diploma.easylearning.description: 'È il diploma dell''azienda cliente'
type_diploma.fundae.name: Fundae
type_diploma.fundae.description: 'È il diploma Fundae'
type_diploma.hobetuz.name: Hobetuz
type_diploma.hobetuz.description: 'È il diploma di Hobetuz'
type_money.euro.name: Euro
type_money.euro.country: Spagna
type_money.dolar_estadounidense.name: 'Dollaro USA'
type_money.dolar_estadounidense.country: 'Stati Uniti'
section_default_front.mi_formacion.name: 'La mia formazione'
section_default_front.mi_formacion.description: 'All''interno della formazione assegnata, potete trovare tutti i corsi che vi sono stati assegnati.'
section_default_front.formacion_adicional.name: 'Formazione aggiuntiva'
section_default_front.formacion_adicional.description: 'In questa sezione si possono trovare tutti i corsi open campus.'
section_default_front.formacion_asignada.name: 'Formazione assegnata'
section_default_front.formacion_asignada.description: 'In questa sezione potete trovare tutti i corsi che vi sono stati assegnati.'
setting.multi_idioma.name: Multilingue
setting.multi_idioma.description: 'Offre un''interfaccia multilingue'
setting.default_lenguage.name: 'Lingua predefinita'
setting.default_lenguage.description: 'Lingua predefinita dell''interfaccia utente del sistema'
setting.languages.name: Lingua
setting.languages.description: 'Lingue disponibili nell''applicazione'
setting.registro_libre.name: 'Registrazione gratuita degli utenti'
setting.registro_libre.description: 'Modalità che consente all''utente di registrarsi liberamente sulla piattaforma dopo aver compilato il modulo corrispondente.'
setting.opinion_plataforma.name: 'Opinioni sulla piattaforma'
setting.opinion_plataforma.description: 'Opinioni sulla piattaforma'
setting.validacion_automatica.name: 'Convalida automatica della registrazione dell''utente'
setting.validacion_automatica.description: 'Convalida automatica della registrazione dell''utente'
setting.filtros_plataforma.name: 'Filtri sulla piattaforma'
setting.filtros_plataforma.description: 'Serve per attivare o disattivare i filtri della piattaforma'
setting.itinearios_plataforma.name: 'Itinerari sulla piattaforma'
setting.itinearios_plataforma.description: 'Serve per attivare o disattivare gli itinerari sulla piattaforma'
setting.seccion_cursos.name: 'Sezioni del corso [FRONT]'
setting.seccion_cursos.description: 'Sezioni del corso [FRONT],'
setting.set_points_course.name: 'Impostare i punti per il corso'
setting.set_points_course.description: 'Si usa per assegnare punti ai corsi quando si creano o si modificano, soprattutto per i corsi di e-learning'
setting.default_points_course.name: 'Punti predefiniti per il corso'
setting.default_points_course.description: 'Se si usa per le formule di gioco, se il capitolo non è un gioco, vengono assegnati metà punti'
setting.documentation_course.name: 'Informazioni generali'
setting.documentation_course.description: 'Attivare froala per aggiungere le informazioni generali del corso nella fase 2 del corso'
setting.open_course.name: 'Corsi aperti'
setting.open_course.description: 'Ciò consente di aggiungere alla piattaforma corsi aperti o formazione aggiuntiva'
setting.client_id.name: 'ID cliente vimeo'
setting.client_id.description: 'Questo è l''identificativo del client vimeo'
setting.client_secret.name: 'Client vimeo segreto'
setting.client_secret.description: 'Client vimeo segreto'
setting.access_token.name: 'Token di accesso'
setting.access_token.description: 'Token di accesso'
setting.user_id.name: 'Id utente'
setting.user_id.description: 'ID utente registrato vimeo'
setting.project_id.name: 'Cartella dei capitoli video'
setting.project_id.description: 'È l''identificatore in cui sono ospitate le risorse dei capitoli video'
setting.project_id_resource_course.name: 'Kit di risorse per i materiali (invito a presentare proposte)'
setting.project_id_resource_course.description: 'È l''identificatore della cartella in cui sono memorizzati i video relativi ai materiali del corso e alla chiamata'
setting.project_id_task_course.name: 'Cartella delle risorse dei compiti'
setting.project_id_task_course.description: 'È l''identificatore della cartella in cui sono memorizzati i video relativi ai compiti e all''appello del corso'
setting.project_id_video_Quiz.name: 'Cartella risorse del videoquiz'
setting.project_id_video_Quiz.description: 'È l''identificatore della cartella in cui sono ospitati i video relativi al gioco videoquiz'
setting.project_id_Roleplay.name: 'Kit di risorse per i giochi di ruolo'
setting.project_id_Roleplay.description: 'Identificato per le risorse di tipo video nel gioco di ruolo'
setting.upload_sudomain.name: 'Caricare sul sottodominio'
setting.upload_sudomain.description: 'Questa variabile viene utilizzata per caricare video e file SCORM, consentendo di superare le restrizioni di Cloudflare'
setting.from_email.name: 'Da e-mail'
setting.from_email.description: 'È l''origine delle e-mail inviate dalla piattaforma'
setting.from_name.name: 'Dal nome della piattaforma'
setting.from_name.description: 'Il nome della piattaforma riportato nelle e-mail e nei diplomi'
setting.from_cif.name: 'Dal CIF'
setting.from_cif.description: 'Numero di partita IVA dell''azienda'
setting.email_support.name: 'Assistenza via e-mail'
setting.email_support.description: 'Queste e-mail vengono utilizzate per inviare notifiche di assistenza'
setting.email_support_register.name: 'Ricezione dell''e-mail dell''amministratore'
setting.email_support_register.description: 'Si tratta di un''e-mail utilizzata per ricevere le richieste di registrazione sulla piattaforma'
setting.news.name: Notizie
setting.news.description: Notizie
setting.foro.name: Forum
setting.foro.description: Forum
setting.desafios.name: Sfide
setting.desafios.description: Sfide
setting.secciones.name: Sezioni
setting.secciones.description: 'Questa variabile consente di configurare se le sezioni sono visualizzate nella parte anteriore della pagina'
setting.encuestas.name: Sondaggi
setting.encuestas.description: Sondaggi
setting.active_cron_exports.name: 'Esportazioni cron attive'
setting.active_cron_exports.description: 'Esportazioni cron attive, generalmente utilizzate per esportare i dati dalla piattaforma'
setting.gender_excel.name: 'Genero excel'
setting.gender_excel.description: 'Utilizzato per aggiungere il genere nell''esportazione di Excel'
setting.code.name: Codice
setting.code.description: 'Mostra il campo codice nell''esportazione delle statistiche dei corsi'
setting.finished_chapters.name: 'Capitoli completati'
setting.finished_chapters.description: 'mostra i capitoli completati nell''esportazione delle statistiche del corso'
setting.zoom_cliente_id.name: 'Zoom ID cliente'
setting.zoom_cliente_id.description: 'ID del client Zoom: necessario per utilizzare l''API Zoom'
setting.zoom_cliente_secret.name: 'Segreto del cliente Zoom'
setting.zoom_cliente_secret.description: 'chiave del client Zoom: necessaria per utilizzare l''API dello zoom'
setting.zoom_account_id.name: 'ID account Zoom'
setting.zoom_account_id.description: 'Numero di conto del cliente Zoom, necessario per utilizzare l''API Zoom'
setting.zoom_email.name: 'Zoom Email'
setting.zoom_email.description: 'posta elettronica del client Zoom: necessaria per utilizzare l''API dello zoom'
setting.clickmeeting_api_key.name: 'Chiave API di ClickMeeting'
setting.clickmeeting_api_key.description: 'ID cliente ClickMeeting: necessario per utilizzare l''API ClickMeeting'
setting.clikmeeting_dirbase.name: 'Directory di base di ClickMeeting'
setting.clikmeeting_dirbase.description: 'Indirizzo del server ClickMeeting'
setting.clikmeeting_events_paralel.name: 'Eventi collaterali di ClickMeeting'
setting.clikmeeting_events_paralel.description: 'Numero di eventi collaterali contrattati'
setting.plugnmeet_serverurl.name: 'URL del server plugNmeet'
setting.plugnmeet_serverurl.description: 'Indirizzo del server plugNmeet'
setting.plugnmeet_api_key.name: 'Chiave API plugNmeet'
setting.plugnmeet_api_key.description: 'ID cliente plugNmeet'
setting.plugnmeet_secret.name: 'Chiave segreta plugNmeet'
setting.plugnmeet_secret.description: 'Chiave client plugNmeet'
setting.plugnmeet_analyticsurl.name: 'Analisi degli URL di plugNmeet'
setting.plugnmeet_analyticsurl.description: 'Indirizzo del server plugNmeet per le analisi'
setting.zoom_urlreports.name: 'URL del rapporto Zoom'
setting.zoom_urlreports.description: 'Indirizzo in cui sono archiviati i rapporti zoom'
setting.plugnmeet_urlreports.name: 'Url di segnalazione plugNmeet'
setting.plugnmeet_urlreports.description: 'Indirizzo in cui sono memorizzati i report di plugNmeet'
setting.clickmeeting_urlreports.name: 'URL di segnalazione di ClickMeeting'
setting.clickmeeting_urlreports.description: 'Indirizzo in cui sono archiviati i report di ClickMeeting'
setting.library_enabled.name: 'Biblioteca abilitata'
setting.library_enabled.description: 'Biblioteca abilitata'
setting.library_audio_local.name: 'Audio locale della biblioteca'
setting.library_audio_local.description: 'Audio locale della biblioteca'
setting.library_audio_path.name: 'Percorso audio della libreria'
setting.library_audio_path.description: 'Percorso audio della libreria'
setting.library_file_path.name: 'Percorso del file di libreria'
setting.library_file_path.description: 'Percorso del file di libreria'
setting.library_data_page_size.name: 'Dimensione della pagina dei dati della biblioteca'
setting.library_data_page_size.description: 'Dimensione della pagina dei dati della biblioteca'
setting.library_comments.name: 'Commenti dalla biblioteca'
setting.library_comments.description: 'Commenti dalla biblioteca'
setting.challenge_loops.name: 'Sfide in loop'
setting.challenge_loops.description: 'Sfide in loop'
setting.points_for_win.name: 'Punti per la vittoria'
setting.points_for_win.description: 'Punti per la vittoria'
setting.points_for_lose.name: 'Punti per la sconfitta'
setting.points_for_lose.description: 'Punti per la sconfitta'
setting.points_fortie.name: 'Punti per il pareggio'
setting.points_fortie.description: 'Punti per il pareggio'
setting.points_corrects.name: 'Punti per la correttezza'
setting.points_corrects.description: 'Punti per la correttezza'
setting.points_for_left.name: 'Punti lasciati'
setting.points_for_left.description: 'Punti lasciati'
setting.total_duels.name: 'Numero totale di duelli'
setting.total_duels.description: 'Numero totale di duelli'
setting.seconds_per_question.name: 'Secondi per domanda'
setting.seconds_per_question.description: 'Secondi per domanda'
setting.user_dni.name: 'ID utente'
setting.user_dni.description: 'Appare quando si crea o si modifica un utente'
setting.edit_code.name: 'Modifica del codice'
setting.edit_code.description: 'È un identificatore unico per l''utente'
setting.stats_acumulative.name: 'Statistiche cumulative'
setting.stats_acumulative.description: 'Questo nel caso in cui si voglia che le statistiche siano cumulative'
setting.maximo_fechas.name: 'Intervallo massimo di date'
setting.maximo_fechas.description: 'Intervallo massimo di date per le consultazioni'
setting.maximo_horas.name: 'Richieste massime all''ora'
setting.maximo_horas.description: 'Richieste massime all''ora'
setting.maximo_dia.name: 'Richieste massime al giorno'
setting.maximo_dia.description: 'Richieste massime al giorno'
setting.fundae.name: Fundae
setting.fundae.description: 'Se questa opzione è attivata, quando viene pubblicata una chiamata gli utenti devono compilare tutti i campi necessari della tabella users_extra_fundae'
setting.margen_entrada.name: 'Margine di ingresso predefinito'
setting.margen_entrada.description: 'Margine di input predefinito, che viene utilizzato nel codice QR'
setting.margen_salida.name: 'Margine di uscita predefinito'
setting.margen_salida.description: 'Margine di uscita predefinito, che viene utilizzato nel codice QR'
setting.registrar_qr.name: 'Registrati con la sessione QR'
setting.registrar_qr.description: 'Se questa opzione è attivata, le sessioni saranno registrate con QR'
setting.maximo_alumnos.name: 'Numero massimo di studenti per gruppo'
setting.maximo_alumnos.description: 'Numero massimo di studenti per gruppo'
setting.min_score.name: 'Punteggio minimo superabile'
setting.min_score.description: 'Punteggio minimo superabile'
setting.types_action.name: 'Tipi di azione'
setting.types_action.description: 'Tipi di azione'
setting.materiales_convocatoria.name: 'Consentire la creazione di materiali negli inviti a presentare proposte'
setting.materiales_convocatoria.description: 'Consentire la creazione di materiali negli inviti a presentare proposte'
setting.tareas_convocatoria.name: 'Consentire la creazione di compiti in un invito a presentare proposte'
setting.tareas_convocatoria.description: 'Consentire la creazione di compiti in un invito a presentare proposte'
setting.minimo_minutos.name: 'Tempo di inattività minimo in minuti'
setting.minimo_minutos.description: 'Tempo di inattività minimo in minuti, si applica agli utenti che si trovano sulla piattaforma'
setting.timezones.name: 'Fusi orari ammessi nell''invito a presentare proposte'
setting.timezones.description: 'Fuso orario che può essere configurato per l''invito a presentare proposte'
catalog.1.name: 'Tipi di capitoli'
catalog.1.description: 'Configurazione dei tipi di capitoli che saranno disponibili sulla piattaforma'
catalog.2.name: 'Tipi di corsi'
catalog.2.description: 'Configurazione dei tipi di corsi che saranno disponibili sulla piattaforma'
catalog.3.name: 'Criteri di approvazione'
catalog.3.description: 'Configurazione dei criteri di approvazione, che saranno disponibili sulla piattaforma'
catalog.4.name: 'Avvisi per tutor'
catalog.4.description: 'Configurazione di avvisi per i tutor, che saranno disponibili sulla piattaforma'
catalog.5.name: 'Tipi di diplomi'
catalog.5.description: 'Configurazione dei tipi di diplomi che saranno disponibili sulla piattaforma'
catalog.6.name: 'Configurazione del client nella chiamata'
catalog.6.description: 'Configurazione dei passi da visualizzare nell''invito a presentare proposte'
catalog.7.name: 'Tipi di monete'
catalog.7.description: 'Configurazione dei tipi di valute che saranno disponibili sulla piattaforma'
catalog.8.name: 'Gruppo di configurazioni'
catalog.8.description: 'Gruppo di configurazioni, che saranno disponibili sulla piattaforma'
catalog.9.name: Configurazioni
catalog.9.description: 'Configurazioni per gruppo, che saranno disponibili in piattaforma'
catalog.10.name: Azienda
catalog.10.description: 'Le aziende utenti, che saranno disponibili sulla piattaforma'
catalog.11.name: 'Categoria professionale'
catalog.11.description: 'Categorie professionali di utenti, che saranno disponibili sulla piattaforma'
catalog.12.name: 'Centro di lavoro utente'
catalog.12.description: 'Posti di lavoro per gli utenti, che saranno disponibili sulla piattaforma'
catalog.13.name: 'Dipartimento di lavoro degli utenti'
catalog.13.description: 'Dipartimenti di lavoro per gli utenti, che saranno disponibili sulla piattaforma'
catalog.14.name: 'Livello di studio dell''utente'
catalog.14.description: 'I livelli di studio degli utenti, che saranno disponibili sulla piattaforma'
catalog.15.name: 'Fasi per i diversi tipi di corsi'
catalog.15.description: 'Configurazione dei passaggi per i diversi tipi di corsi, che saranno disponibili sulla piattaforma'
catalog.16.name: 'Tipi di aule virtuali'
catalog.16.description: 'Tipi di aule virtuali per i diversi tipi di corsi, che saranno disponibili sulla piattaforma'
catalog.17.name: 'Tipi di identificazione'
catalog.17.description: 'Tipi di identificazione disponibili sulla piattaforma'
nps_question.text.name: Testo
nps_question.text.descripction: 'Dateci la vostra opinione'
setting.help.user.name: 'Includere il pdf della guida nel menu utente'
setting.help.user.description: 'Questo aiuto è stato creato appositamente per iberostar'
catalog.18.name: 'Modalità per le chiamate faccia a faccia'
catalog.18.description: 'Si tratta di un''esigenza particolare per Iberostar'
setting.userPolicies_plataforma.name: 'Informativa sulla privacy'
setting.userPolicies_plataforma.description: 'Questa variabile viene utilizzata per attivare un modal sul front-end quando l''utente non accetta l''informativa sulla privacy'
setting.course.tab.person.name: 'Scheda persone dettagli del corso'
setting.course.tab.stats.name: "Tabella statistica dettagli del corso\n"
setting.course.tab.opinions.name: "Scheda di opinioni Dettagli del corso\n"
setting.documentation.name: Documentazione
setting.documentation.description: 'Attivare il modulo Documentazione nel menu laterale'
setting.user_company.name: Aziende
setting.user_company.description: 'Attivare il modulo Aziende nel menu laterale'
setting.pages.name: 'Piè di pagina'
setting.pages.description: 'Attivare il piè di pagina nel campus'
setting.lite_formation.name: 'Gruppo di formazione sulle statistiche generali'
setting.lite_formation.description: 'Gruppo di formazione sulle statistiche generali'
setting.lite_formation.formationHours.name: 'Ore di formazione'
setting.lite_formation.formationHours.description: 'Ore totali di formazione e ore medie di formazione per persona'
setting.lite_formation.peopleWithCourses.name: 'Persone con corsi'
setting.lite_formation.peopleWithCourses.description: 'Persone in corso di formazione e persone che hanno completato almeno un corso'
setting.lite_formation.courseStartedAndFinished.name: 'Corsi iniziati, in corso e completati'
setting.lite_formation.courseStartedAndFinished.description: 'Numero di corsi iniziati, in corso e completati'
setting.lite_formation.requiredCourses.name: 'Corsi obbligatori'
setting.lite_formation.requiredCourses.description: 'Corsi obbligatori assegnati a un bando o a un percorso'
setting.lite_formation.general.name: Generale
setting.lite_formation.general.description: Generale
setting.lite_formation.openedCourses.name: 'Corsi aperti'
setting.lite_formation.openedCourses.description: 'Corsi volontari'
setting.lite_formation.educativeStatus.name: 'Livello di istruzione'
setting.lite_formation.educativeStatus.description: 'Stato della formazione per livelli di punteggio'
setting.lite_formation.gamifiedPills.name: 'Pillole gamificate'
setting.lite_formation.gamifiedPills.description: 'Numero di capitoli gamificati, fallimenti e successi nei quiz gamificati'
setting.lite_formation.gamifiedTest.name: 'Pillole di prova'
setting.lite_formation.gamifiedTest.description: 'Test gamificati utilizzati e risultati positivi e negativi per tipo di test'
setting.lite_formation.peoplePerformance.name: 'Prestazioni delle persone'
setting.lite_formation.peoplePerformance.description: 'Prestazioni delle persone'
setting.lite_formation.coursesByStars.name: 'Corsi per punteggio'
setting.lite_formation.coursesByStars.description: 'Valutazione a stelle dei corsi'
setting.lite_formation.structureAndHotel.name: 'Appartamenti e hotel'
setting.lite_formation.structureAndHotel.description: 'Percentuale per gruppo'
setting.lite_formation.schoolFinishedAndProgress.name: 'Scuola Completato e in corso'
setting.lite_formation.schoolFinishedAndProgress.description: 'Scuola con maggior partecipazione, corsi in corso e completati'
setting.lite_formation.coursesBySchool.name: 'Corsi per scuola'
setting.lite_formation.coursesBySchool.description: 'Numero di corsi per categoria'
setting.lite_formation.coursesByDepartment.name: 'Corsi per dipartimento'
setting.lite_formation.coursesByDepartment.description: 'Creazione di corsi per dipartimento'
setting.lite_formation.usersMoreActivesByCourses.name: 'Utenti più attivi per corso'
setting.lite_formation.usersMoreActivesByCourses.description: 'Persone più e meno attive nei corsi completati'
setting.lite_evolution.name: 'Gruppo di sviluppo delle statistiche generali'
setting.lite_evolution.description: 'Gruppo di sviluppo delle statistiche generali'
setting.lite_evolution.trainedPerson.name: 'Persone formate'
setting.lite_evolution.trainedPerson.description: 'Persone che hanno completato almeno un corso'
setting.lite_evolution.startedCourses.name: 'Corsi iniziati'
setting.lite_evolution.startedCourses.description: 'Corsi iniziati'
setting.lite_evolution.proccessCourses.name: 'Corsi in corso'
setting.lite_evolution.proccessCourses.description: 'Corsi in corso'
setting.lite_evolution.finishedCourses.name: 'Corsi completati'
setting.lite_evolution.finishedCourses.description: 'Corsi completati'
setting.lite_evolution.segmentedHours.name: 'Segmentazione delle ore'
setting.lite_evolution.userNewInPlatformThanFinishedOneCourse.name: 'Nuovi utenti che hanno completato un corso'
setting.lite_evolution.userNewInPlatformThanFinishedOneCourse.description: 'Persone nuove alla piattaforma che abbiano completato almeno un corso'
setting.lite_demography.name: 'Gruppo Demografia in Statistica Generale'
setting.lite_demography.description: 'Gruppo Demografia in Statistica Generale'
setting.lite_demography.usersBySexAndAge.name: 'Utenti per sesso ed età'
setting.lite_demography.usersBySexAndAge.description: 'Utenti per sesso ed età'
setting.lite_demography.ageDistribution.name: 'Distribuzione per età'
setting.lite_demography.ageDistribution.description: 'Distribuzione per età'
setting.lite_demography.deviceDistribution.name: 'Distribuzione per dispositivo'
setting.lite_demography.deviceDistribution.description: 'Distribuzione per dispositivo'
setting.lite_demography.usersByCountries.name: 'Distribuzione per paese'
setting.lite_demography.usersByCountries.description: 'Distribuzione per paese'
setting.lite_activity.name: 'Gruppo di attività Statistiche generali'
setting.lite_activity.description: 'Gruppo di attività Statistiche generali'
setting.lite_activity.activityInfo.name: 'Informazioni sull''attività'
setting.lite_activity.activityInfo.description: 'Persone attive sul portale, persone registrate, persone che hanno effettuato il login almeno una volta negli ultimi 30 giorni, persone disattivate e persone che non hanno mai effettuato il login'
setting.lite_activity.accessDays.name: 'Accesso per giorni'
setting.lite_activity.accessDays.description: 'Giorni di accesso'
setting.lite_activity.platformAccessByHours.name: 'Accesso per piattaforma e orario'
setting.lite_activity.platformAccessByHours.description: 'Tempi di accesso alla piattaforma per giorno e ora (mappa di calore)'
setting.lite_activity.courseStartTime.name: 'Distribuzione per orari di inizio dei corsi'
setting.lite_activity.courseStartTime.description: 'Orari di inizio del corso'
setting.lite_activity.courseEndTime.name: 'Distribuzione per ore di completamento del corso'
setting.lite_activity.courseEndTime.description: 'Ore di completamento del corso (mappa di calore)'
setting.lite_activity.coursesStartedVsFinished.name: 'Corsi iniziati e corsi completati'
setting.lite_activity.coursesStartedVsFinished.description: 'Corsi iniziati vs. corsi completati'
setting.lite_activity.usersMoreActivesByActivity.name: 'Utenti più attivi'
setting.lite_activity.usersMoreActivesByActivity.description: 'Le persone più e meno attive e il loro tempo trascorso sulla piattaforma'
setting.lite_itinerary.name: 'Itinerari nel gruppo Statistiche generali'
setting.lite_itinerary.description: 'Itinerari nel gruppo Statistiche generali'
setting.lite_itinerary.itinerariesStartedAndFinished.name: 'Avvio e completamento di itinerari'
setting.lite_itinerary.itinerariesStartedAndFinished.description: 'Itinerari iniziati e completati'
setting.lite_itinerary.itinerariesCompletedByCountries.name: 'Itinerari completati per paese'
setting.lite_itinerary.itinerariesCompletedByCountries.description: 'Itinerari completati per paese'
setting.survey.hide_empty_comment.name: 'Nascondere l''opinione con un commento vuoto'
setting.survey.hide_empty_comment.description: 'Sondaggi, nascondere il nome quando il commento è vuoto'
setting.survey.show_only_ratings.name: 'Sondaggio, mostrare solo il nome delle qualifiche'
setting.survey.show_only_ratings.description: 'Il sondaggio mostra solo il nome delle qualifiche'
app.survey.post_nps.enabled.name: 'Pubblicazione di un sondaggio sul nome abilitato nps'
app.survey.post_nps.enabled.description: 'Nome della pubblicazione dell''indagine dell''applicazione abilitata nps'
setting.lite_evolution.segmentedHours.description: Orario
setting.course.tab.person.description: 'Persone, dettagli del corso'
setting.course.showDeactivatedCourses.name: 'La scheda mostra il nome dei corsi disattivati'
setting.course.showDeactivatedCourses.description: 'Visualizza i nomi dei corsi disattivati'
catalog.19.name: 'Amministratore delle traduzioni'
catalog.19.description: 'Traduzioni dell''amministratore'
setting.lenguage.platform: 'Traduzioni dell''amministratore'
setting.module.announcement.name: 'Invito a presentare candidature'
setting.module.announcement.description: 'Attivate il modulo chiamate nel sottomenu corsi'
course.diploma.index: 'Indice dei contenuti'
setting.zip.day_available_until.name: 'Giorni disponibili'
setting.zip.day_available_until.description: 'Numero di giorni disponibili prima che lo zip venga automaticamente cancellato.'
catalog.20.name: 'Chiamata di campi extra'
course.diploma.filters: 'Attivare filtri aggiuntivi nel rapporto sul diploma'
setting.lenguage.platform.description: 'Lingue disponibili nel pannello dell''amministratore'
translations_admin.title1: 'Formazione assegnata'
translations_admin.title2: 'Formazione aggiuntiva'
translations_admin.title3: 'Corsi assegnati'
translations_admin.title4: 'Corsi volontari'
setting.course.tab.stats.description: 'Abilitato attiva la sezione "Statistiche" al livello dettagliato di un corso.'
setting.course.tab.options.description: 'Abilitato attiva la sezione "Feedback" a livello di dettaglio del corso.'
course.diploma.index.description: 'Abilitata, la sezione "Diplomi" si attiva quando si crea/modifica un corso'
setting.use.filter_in_ranking.name: 'Utilizzare i filtri nella classifica degli utenti'
setting.use.filter_in_ranking.description: 'Permette di selezionare dal menu i filtri delle categorie con cui un utente desidera essere confrontato. Se questa opzione è disattivata, l''utente sarà confrontato di default con tutti i filtri disponibili sulla piattaforma'
setting.use.include_only_first_category_name: 'Mostra solo la prima categoria del filtro nelle classifiche'
setting.use.include_only_first_category_description: 'Se attivo, viene visualizzato solo il primo collegamento dell''utente. Altrimenti, vengono visualizzate tutte le categorie associate. Ad esempio, se la categoria è "Paese", l''utente potrebbe essere collegato sia alla Spagna che al Nicaragua.'
setting.email_support_error.name: 'Posta elettronica di supporto per gli errori'
setting.email_support_error.description: 'Posta elettronica a cui inviare gli incidenti della piattaforma'
setting.export.task.slot_quantity.name: 'Numero di slot di attività per utente'
setting.export.task.slot_quantity.description: 'Numero di slot disponibili per l''elaborazione delle attività di esportazione per utente.'
setting.export.task.long_running_type_tasks.name: 'Tipi di compiti a lungo termine'
setting.export.task.long_running_type_tasks.description: 'Elenco dei tipi di compiti considerati di lunga durata per l''esportazione.'
setting.export.zip_task.slot_quantity.name: 'Numero di slot per task zip per utente'
setting.export.zip_task.slot_quantity.description: 'Numero di slot disponibili per l''elaborazione di attività di compressione zip per utente.'
setting.export.zip_task.long_running_type_tasks.name: 'Tipi di attività zip a lungo termine'
setting.export.zip_task.long_running_type_tasks.description: 'Elenco dei tipi di compiti zip considerati di lunga durata.'
setting.export.task.user_pending_max_count_task.name: 'Numero massimo di attività in sospeso per utente'
setting.export.task.user_pending_max_count_task.description: 'Numero massimo di compiti in attesa che un utente può avere in coda.'
setting.export.task.timeout.name: 'Limite di tempo per i compiti'
setting.export.task.timeout.description: 'Tempo massimo in secondi prima che un''attività di esportazione sia considerata scaduta.'
setting.export.zip_task.timeout.name: 'Limite di tempo per le attività di zip'
setting.export.zip_task.timeout.description: 'Tempo massimo in secondi prima che un''attività di compressione zip sia considerata scaduta.'
setting.export.task.timeout_seconds.name: 'Tempo di timeout per attività in stato di TIMEOUT'
setting.export.task.timeout_seconds.description: 'Tempo massimo in secondi dopo il quale un task in stato di TIMEOUT non è più considerato in esecuzione.'
type_diploma.novomatic.name: Novomatic
type_diploma.novomatic.description: 'È il diploma personalizzato di Novomatic'
app.announcement.managers.sharing.name: 'Consentire la creazione di compiti in un invito a presentare proposte'
app.announcement.managers.sharing.description: 'Consentire la creazione di compiti in un invito a presentare proposte'
