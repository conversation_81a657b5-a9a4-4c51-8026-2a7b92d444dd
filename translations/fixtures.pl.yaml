nps_question.text.description: '<PERSON><PERSON><PERSON><PERSON> nam swoją opinię'
nps_question.nps.description: '<PERSON><PERSON><PERSON> kursu'
type_course.teleformacion.name: E-learning
type_course.teleformacion.description: 'W przypadku kursów e-learningowych'
type_course.presencial.name: '<PERSON> miej<PERSON>'
type_course.presencial.description: '<PERSON><PERSON><PERSON> na miejscu'
type_course.mixto.name: Mi<PERSON>zane
type_course.mixto.description: 'Jest to połączenie e-learningu i szkoleń bezpośrednich'
type_course.aula_virtual.name: 'Wirtualna klasa'
type_course.aula_virtual.description: '<PERSON><PERSON><PERSON><PERSON> odbywają się za pośrednictwem wideokonferencji'
alert_type_tutor.1.name: '<PERSON><PERSON><PERSON>, do której zadzwoniono, nie uzyskała dostępu do kursu'
alert_type_tutor.1.description: 'Powiadomienie zostanie wysłane do opiekuna, jeśli wezwana osoba nie uzy<PERSON>ła dostępu do kursu'
alert_type_tutor.2.name: '<PERSON>ł<PERSON>ęło 50% czasu rozmowy, a 25% treści nie zostało ukończone'
alert_type_tutor.2.description: 'Powiadomienie zostanie wysłane do opiekuna, jeśli upłynie 50% czasu rozmowy, a 25% treści nie zostało ukończone'
alert_type_tutor.3.name: 'Upłynęło 80% czasu rozmowy, a 50% treści nie zostało ukończone'
alert_type_tutor.3.description: 'Powiadomienie zostanie wysłane do opiekuna, jeśli upłynie 80% czasu rozmowy, a 50% treści nie zostało ukończone'
alert_type_tutor.4.name: 'Do końca rozmowy pozostało już tylko kilka dni, a kurs nie został jeszcze ukończony'
alert_type_tutor.4.description: 'Musisz ocenić liczbę dni, które są uważane za kilka dni'
alert_type_tutor.5.name: 'Osoba, do której zadzwoniono, ukończyła kurs, ale nie odpowiedziała na ankietę.'
alert_type_tutor.5.description: 'Jeśli platforma zawiera ankiety, powiadomienie zostanie wysłane do opiekuna, jeśli wywołana osoba ukończyła kurs, ale nie odpowiedziała na ankietę'
alert_type_tutor.6.name: 'Osoba ta ukończyła kurs, ale nie pobrała dyplomu'
alert_type_tutor.6.description: 'Powiadomienie zostanie wysłane do opiekuna, jeśli wezwana osoba ukończyła kurs, ale nie pobrała dyplomu.'
announcement_configuration_type.temporalizacion.name: Czas
announcement_configuration_type.temporalizacion.description: 'Aby ułatwić monitorowanie kursu, przypiszemy czas do każdego bloku treści i działań, dzięki czemu będziemy w stanie wykryć, którzy uczestnicy pracują w odpowiednim tempie lub pozostają w tyle w procesie szkolenia'
announcement_configuration_type.curso_bonificado.name: 'Kurs subsydiowany'
announcement_configuration_type.curso_bonificado.description: 'Subsydiowane kursy to te, które są prowadzone za pośrednictwem Fundacji Trójstronnej i są finansowane przez firmy ze składek na ubezpieczenie społeczne.'
announcement_configuration_type.chat.name: Czat
announcement_configuration_type.chat.description: 'Czat to narzędzie do komunikacji synchronicznej, które umożliwia uczestnikom kursu interakcję w czasie rzeczywistym za pośrednictwem wiadomości tekstowych.'
announcement_configuration_type.notificaciones.name: Powiadomienia
announcement_configuration_type.notificaciones.description: 'Powiadomienia to wiadomości wysyłane do uczestników kursu w celu poinformowania ich o ważnych wiadomościach lub wydarzeniach.'
announcement_configuration_type.mensajeria.name: Wiadomości
announcement_configuration_type.mensajeria.description: 'System wiadomości to system komunikacji, który umożliwia uczestnikom kursu wysyłanie i odbieranie prywatnych wiadomości.'
announcement_configuration_type.foros.name: Fora
announcement_configuration_type.foros.description: 'Forum to asynchroniczna przestrzeń komunikacyjna, która umożliwia uczestnikom kursu wymianę wiadomości na dany temat.'
announcement_configuration_type.diploma.name: Dyplom
announcement_configuration_type.diploma.description: 'Dyplomy to certyfikaty przyznawane uczestnikom kursu jako dowód jego ukończenia.'
announcement_configuration_type.tutor_alerts.name: 'Aktywuj alerty korepetytora'
announcement_configuration_type.tutor_alerts.description: 'Alerty to wiadomości wysyłane do opiekuna kursu w celu poinformowania go o ważnych wiadomościach lub wydarzeniach.'
announcement_configuration_type.encuesta_satisfaccion.name: 'Badanie satysfakcji'
announcement_configuration_type.encuesta_satisfaccion.description: 'Ankiety satysfakcji to kwestionariusze wysyłane do uczestników kursów, aby dowiedzieć się, co sądzą o kursie.'
announcement_configuration_type.finalizar_convocatoria.name: 'Kurs pozostanie aktywny po zakończeniu połączenia'
announcement_configuration_type.finalizar_convocatoria.description: 'Użytkownik będzie mógł uzyskać dostęp do treści kursu po jego zakończeniu.'
announcement_configuration_type.firma_digital.name: 'Podpis cyfrowy'
announcement_configuration_type.firma_digital.description: 'Podpis cyfrowy jest wymagany, aby móc podpisać uczestnictwo w kursie bezpośrednim.'
announcement_configuration_type.gestion_costes.name: 'Zarządzanie kosztami'
announcement_configuration_type.gestion_costes.description: 'Zarządzanie kosztami umożliwia grupom wskazanie kosztu połączenia.'
announcement_configuration_type.EMAIL_NOTIFICATION_ON_ANNOUNCEMENT.name: EMAIL_NOTIFICATION_ON_ANNOUNCEMENT
announcement_configuration_type.EMAIL_NOTIFICATION_ON_ANNOUNCEMENT.description: 'Włącz powiadomienia e-mail.'
announcement_configuration_type.NOTIFICATION_ON_ANNOUNCEMENT.name: NOTIFICATION_ON_ANNOUNCEMENT
announcement_configuration_type.NOTIFICATION_ON_ANNOUNCEMENT.description: 'Włącz normalne powiadomienia'
announcement_configuration_type.objetivos_contenidos.name: 'Cele i zawartość'
announcement_configuration_type.objetivos_contenidos.description: 'Cele i treść kursu zostaną uwzględnione w dyplomie.'
announcement_configuration_type.dni.name: Dni
announcement_configuration_type.dni.description: 'Identyfikator studenta zostanie umieszczony na dyplomie'
announcement_configuration_type.template_excel.name: 'Szablon rejestracji excel'
announcement_configuration_type.template_excel.description: 'Ten szablon będzie używany do rejestracji studentów, przy użyciu kodu HRBP zamiast DNI.'
announcement_configuration_type.report_zip.name: ZIP
announcement_configuration_type.report_zip.description: 'Umożliwienie opiekunowi pobierania raportów grupowych w formacie ZIP'
announcement_criteria.1.name: 'Kompletne rozdziały'
announcement_criteria.1.description: '% rozdziałów wymaganych do spełnienia kryterium zaliczenia zaproszenia.'
announcement_criteria.2.name: 'Wykonywanie zadań'
announcement_criteria.2.description: '% zadań wymaganych do spełnienia kryterium zaliczenia połączenia.'
announcement_criteria.3.name: 'Maksymalny czas przestoju'
announcement_criteria.3.description: 'Po osiągnięciu wyznaczonego czasu spędzanego przed ekranem bez żadnej aktywności, odliczanie czasu zostanie wstrzymane do momentu ponownego wykrycia aktywności użytkownika.'
announcement_criteria.4.name: 'Ukończenie działań'
announcement_criteria.4.description: 'Po osiągnięciu wyznaczonego czasu spędzanego przed ekranem bez żadnej aktywności, odliczanie czasu zostanie wstrzymane do momentu ponownego wykrycia aktywności użytkownika.'
announcement_criteria.5.name: 'Czas ukończony'
announcement_criteria.5.description: '% godzin wymaganych do spełnienia kryterium zaliczenia rozmowy.'
announcement_step_creation.ANNOUNCEMENT_COURSE.description: 'Pierwszy krok w tworzeniu połączenia'
announcement_step_creation.ANNOUNCEMENT_GENERAL_INFO.description: 'Drugi etap tworzenia połączenia, w którym wypełniane są informacje ogólne'
announcement_step_creation.ANNOUNCEMENT_BONUS.description: 'Ten krok zależy od tego, czy klient aktywował bonus'
announcement_step_creation.ANNOUNCEMENT_STUDENTS.description: 'W tym miejscu do połączenia dodawani są uczniowie, którzy mogą zostać przypisani do grupy'
announcement_step_creation.ANNOUNCEMENT_GROUPS.description: 'W tym kroku konfigurowane są grupy studentów utworzone w poprzednim kroku'
announcement_step_creation.ANNOUNCEMENT_COMMUNICATION.description: 'W tym kroku konfigurowana jest ankieta, która ma zostać wysłana do studentów'
announcement_step_creation.ANNOUNCEMENT_SURVEY.description: 'W tym kroku konfigurowana jest ankieta, która ma zostać wysłana do studentów'
announcement_step_creation.ANNOUNCEMENT_CERTIFICATE.description: 'W tym kroku wyświetlane są dyplomy dostępne na platformie, aby klient mógł wybrać ten, który chce'
announcement_step_creation.ANNOUNCEMENT_ALERTS.description: 'Alerty te są specjalnymi alertami informującymi opiekuna o zdarzeniach związanych z połączeniem'
class_room_virtual.zoom.name: Zoom
class_room_virtual.zoom.description: 'Internetowa platforma do konferencji internetowych, umożliwiająca połączenia wideo w wysokiej rozdzielczości, z funkcjami udostępniania pulpitu, tablicy, czatu, nagrywania konferencji, udostępniania dokumentów i dostępu z dowolnego miejsca, ponieważ jest dostępna dla urządzeń mobilnych.'
class_room_virtual.clickmeeting.name: ClickMeeting
class_room_virtual.clickmeeting.description: 'Platforma ClickMeeting jest jednym z najbardziej przyjaznych dla użytkownika interfejsów webinarowych na rynku i oferuje wiele elastycznych opcji dostosowywania'
class_room_virtual.jitsi.name: Jitsi
class_room_virtual.jitsi.description: 'Rozwiązanie Open Source do wideokonferencji z szyfrowanymi połączeniami, dostępne dla różnych systemów operacyjnych'
class_room_virtual.plugnmeet.name: PlugnMeet
class_room_virtual.plugnmeet.description: 'Łatwe w integracji i wysoce konfigurowalne oprogramowanie open source do wideokonferencji'
configuration_cliente_announcement.COMMUNICATION.description: 'Umożliwia to włączenie komunikacji w ramach połączenia'
configuration_cliente_announcement.CERTIFICATE.description: 'Umożliwia to pobieranie dyplomów w ramach połączenia'
configuration_cliente_announcement.SURVEY.description: 'Umożliwia to włączenie ankiet, które zostaną udostępnione później w zaproszeniu do składania wniosków'
configuration_cliente_announcement.ALERT.description: 'Umożliwia to włączenie alertów, które będą dziedziczone w sekcji alertów w samouczku'
configuration_cliente_announcement.TEMPORALIZATION.description: 'Umożliwia temporalizację rozdziałów w ramach zaproszenia do składania wniosków'
configuration_cliente_announcement.BONIFICATION.description: 'Bonus za wezwanie, zwłaszcza za wezwanie trójstronnej fundacji'
configuration_cliente_announcement.ACCESS_CONTENT.description: 'Umożliwia dostęp do treści połączenia po jego zakończeniu'
configuration_cliente_announcement.DIGITAL_SIGNATURE.description: 'Umożliwia włączenie podpisu cyfrowego w zaproszeniu do składania wniosków, zwłaszcza w przypadku kursów bezpośrednich'
configuration_cliente_announcement.COST.description: 'Umożliwienie klientom przypisania kosztów do połączenia.'
configuration_cliente_announcement.NOTIFICATION_ACTIVATE_ANNOUNCEMENT.description: 'Powiadomienia o aktywacji połączenia (e-mail, powiadomienie z przodu)'
configuration_cliente_announcement.CONFIGURATION_IBEROSTAR.description: 'Ta konfiguracja została zaprojektowana specjalnie dla klientów Iberostar, aby nie wpływać na przepływ środków'
configuration_cliente_announcement.REPORT.description: 'Umożliwienie raportowania w zaproszeniach do składania wniosków'
type_course_announcement_step_creation.seleccionar_curso.name: 'Wybierz kurs'
type_course_announcement_step_creation.seleccionar_curso.description: 'Wybierz kurs, dla którego ma zostać utworzone połączenie'
type_course_announcement_step_creation.convocatoria.name: 'Zaproszenie do składania wniosków'
type_course_announcement_step_creation.convocatoria.description: 'Informacje na temat zaproszenia do składania wniosków'
type_course_announcement_step_creation.bonificacion.name: Bonus
type_course_announcement_step_creation.bonificacion.description: 'Informacje na temat zaproszenia do składania wniosków'
type_course_announcement_step_creation.alumnado.name: Absolwenci
type_course_announcement_step_creation.alumnado.description: 'Studenci są dodawani do kursu'
type_course_announcement_step_creation.grupos.name: Grupy
type_course_announcement_step_creation.grupos.description: 'Informacje o grupie są szczegółowe i dodawany jest również opiekun'
type_course_announcement_step_creation.comunicacion.name: Komunikacja
type_course_announcement_step_creation.comunicacion.description: 'Ten krok zależy od tego, czy klient aktywował komunikację.'
type_course_announcement_step_creation.encuesta.name: Ankieta
type_course_announcement_step_creation.encuesta.description: 'Ten krok zależy od tego, czy klient aktywował ankietę, czy nie.'
type_course_announcement_step_creation.diploma.name: Dyplom
type_course_announcement_step_creation.diploma.description: 'Ten krok zależy od tego, czy klient ma aktywowany dyplom, czy nie.'
type_course_announcement_step_creation.alertas.name: Alerty
type_course_announcement_step_creation.alertas.description: 'Może to zależeć od konfiguracji klienta.'
type_diploma.easylearning.name: Domyślne
type_diploma.easylearning.description: 'Jest to dyplom firmy będącej klientem'
type_diploma.fundae.name: Fundae
type_diploma.fundae.description: 'Jest to dyplom Fundae'
type_diploma.hobetuz.name: Hobetuz
type_diploma.hobetuz.description: 'Jest to dyplom Hobetuz'
type_money.euro.name: Euro
type_money.euro.country: Hiszpania
type_money.dolar_estadounidense.name: 'Dolar amerykański'
type_money.dolar_estadounidense.country: 'Stany Zjednoczone'
section_default_front.mi_formacion.name: 'Mój trening'
section_default_front.mi_formacion.description: 'W ramach przypisanego szkolenia można znaleźć wszystkie przypisane kursy.'
section_default_front.formacion_adicional.name: 'Dodatkowe szkolenie'
section_default_front.formacion_adicional.description: 'W tej sekcji można znaleźć wszystkie kursy w otwartym kampusie.'
section_default_front.formacion_asignada.name: 'Przydzielone szkolenie'
section_default_front.formacion_asignada.description: 'W tej sekcji można znaleźć wszystkie przypisane kursy.'
setting.multi_idioma.name: Wielojęzyczny
setting.multi_idioma.description: 'Oferuje wielojęzyczny interfejs'
setting.default_lenguage.name: 'Domyślny język'
setting.default_lenguage.description: 'Domyślny język interfejsu użytkownika systemu'
setting.languages.name: Język
setting.languages.description: 'Języki dostępne w aplikacji'
setting.registro_libre.name: 'Bezpłatna rejestracja użytkownika'
setting.registro_libre.description: 'Tryb umożliwiający użytkownikowi swobodną rejestrację na platformie po wypełnieniu odpowiedniego formularza.'
setting.opinion_plataforma.name: 'Opinie o platformie'
setting.opinion_plataforma.description: 'Opinie o platformie'
setting.validacion_automatica.name: 'Automatyczna weryfikacja rejestracji użytkownika'
setting.validacion_automatica.description: 'Automatyczna weryfikacja rejestracji użytkownika'
setting.filtros_plataforma.name: 'Filtry na platformie'
setting.filtros_plataforma.description: 'Służy do aktywacji lub dezaktywacji filtrów na platformie'
setting.itinearios_plataforma.name: 'Trasy podróży na platformie'
setting.itinearios_plataforma.description: 'Służy do aktywacji lub dezaktywacji tras na platformie'
setting.seccion_cursos.name: 'Sekcje kursu [FRONT]'
setting.seccion_cursos.description: 'Sekcje kursu [FRONT],'
setting.set_points_course.name: 'Ustaw punkty dla kursu'
setting.set_points_course.description: 'Służy do przypisywania punktów do kursów podczas ich tworzenia lub edytowania, zwłaszcza w przypadku kursu e-learningowego'
setting.default_points_course.name: 'Domyślne punkty za kurs'
setting.default_points_course.description: 'W przypadku formuł gier, jeśli rozdział nie jest grą, przyznawana jest połowa punktów'
setting.documentation_course.name: 'Informacje ogólne'
setting.documentation_course.description: 'Aktywuj froala, aby dodać ogólne informacje o kursie w kroku 2 kursu'
setting.open_course.name: 'Kursy otwarte'
setting.open_course.description: 'Pozwala to na dodanie do platformy otwartych kursów lub dodatkowych szkoleń'
setting.client_id.name: 'Identyfikator klienta vimeo'
setting.client_id.description: 'Jest to identyfikator klienta vimeo'
setting.client_secret.name: 'Tajny klient vimeo'
setting.client_secret.description: 'Tajny klient vimeo'
setting.access_token.name: 'Token dostępu'
setting.access_token.description: 'Token dostępu'
setting.user_id.name: 'Identyfikator użytkownika'
setting.user_id.description: 'Identyfikator zarejestrowanego użytkownika vimeo'
setting.project_id.name: 'Folder rozdziałów wideo'
setting.project_id.description: 'Jest to identyfikator, pod którym hostowane są zasoby rozdziałów wideo'
setting.project_id_resource_course.name: 'Zestaw materiałów (zaproszenie do składania wniosków)'
setting.project_id_resource_course.description: 'Jest to identyfikator folderu, w którym przechowywane są materiały wideo związane z kursem i rozmową'
setting.project_id_task_course.name: 'Folder zasobów zadań'
setting.project_id_task_course.description: 'Jest to identyfikator folderu, w którym przechowywane są filmy związane z zadaniami kursu i połączeniami'
setting.project_id_video_Quiz.name: 'Folder zasobów wideoquizu'
setting.project_id_video_Quiz.description: 'Jest to identyfikator folderu, w którym przechowywane są filmy związane z grą wideoquiz'
setting.project_id_Roleplay.name: 'Zestaw zasobów do odgrywania ról'
setting.project_id_Roleplay.description: 'Zidentyfikowane zasoby typu wideo w odgrywaniu ról'
setting.upload_sudomain.name: 'Prześlij do subdomeny'
setting.upload_sudomain.description: 'Ta zmienna jest używana do przesyłania filmów i plików SCORM, umożliwiając obejście ograniczeń Cloudflare'
setting.from_email.name: 'Z wiadomości e-mail'
setting.from_email.description: 'Jest to źródło wiadomości e-mail wysyłanych z platformy'
setting.from_name.name: 'Od nazwy platformy'
setting.from_name.description: 'Nazwa platformy wyświetlana w wiadomościach e-mail i na dyplomach'
setting.from_cif.name: 'Od CIF'
setting.from_cif.description: 'Numer VAT firmy'
setting.email_support.name: 'Wsparcie przez e-mail'
setting.email_support.description: 'Te wiadomości e-mail są używane do wysyłania powiadomień pomocy technicznej'
setting.email_support_register.name: 'Odbiór wiadomości e-mail administratora'
setting.email_support_register.description: 'Jest to wiadomość e-mail używana do otrzymywania próśb o rejestrację na platformie'
setting.news.name: Aktualności
setting.news.description: Aktualności
setting.foro.name: Forum
setting.foro.description: Forum
setting.desafios.name: Wyzwania
setting.desafios.description: Wyzwania
setting.secciones.name: Sekcje
setting.secciones.description: 'Ta zmienna pozwala skonfigurować, czy sekcje mają być wyświetlane z przodu'
setting.encuestas.name: Ankiety
setting.encuestas.description: Ankiety
setting.active_cron_exports.name: 'Aktywny eksport cron'
setting.active_cron_exports.description: 'Aktywny eksport cron, zwykle używany do eksportowania danych z platformy'
setting.gender_excel.name: 'Genero excel'
setting.gender_excel.description: 'Służy do dodawania płci w eksportowanych Excelach'
setting.code.name: Kod
setting.code.description: 'Pokaż pole kodu w eksporcie statystyk kursu'
setting.finished_chapters.name: 'Ukończone rozdziały'
setting.finished_chapters.description: 'pokaż ukończone rozdziały w eksporcie statystyk kursu'
setting.zoom_cliente_id.name: 'Powiększ identyfikator klienta'
setting.zoom_cliente_id.description: 'Identyfikator klienta Zoom - wymagany do korzystania z interfejsu API Zoom'
setting.zoom_cliente_secret.name: 'Tajemnica klienta Zoom'
setting.zoom_cliente_secret.description: 'klucz klienta Zoom - niezbędny do korzystania z interfejsu API Zoom'
setting.zoom_account_id.name: 'Identyfikator konta Zoom'
setting.zoom_account_id.description: 'Numer konta klienta Zoom - wymagany do korzystania z interfejsu API Zoom'
setting.zoom_email.name: 'Zoom Email'
setting.zoom_email.description: 'poczta klienta zoomu - wymagana do korzystania z interfejsu API zoomu'
setting.clickmeeting_api_key.name: 'Klucz API ClickMeeting'
setting.clickmeeting_api_key.description: 'Identyfikator klienta ClickMeeting - wymagany do korzystania z interfejsu API ClickMeeting'
setting.clikmeeting_dirbase.name: 'Katalog podstawowy ClickMeeting'
setting.clikmeeting_dirbase.description: 'Adres serwera ClickMeeting'
setting.clikmeeting_events_paralel.name: 'Wydarzenia towarzyszące ClickMeeting'
setting.clikmeeting_events_paralel.description: 'Liczba zakontraktowanych zdarzeń niepożądanych'
setting.plugnmeet_serverurl.name: 'Adres URL serwera plugNmeet'
setting.plugnmeet_serverurl.description: 'Adres serwera plugNmeet'
setting.plugnmeet_api_key.name: 'Klucz API plugNmeet'
setting.plugnmeet_api_key.description: 'Identyfikator klienta plugNmeet'
setting.plugnmeet_secret.name: 'Tajny klucz plugNmeet'
setting.plugnmeet_secret.description: 'Klucz klienta plugNmeet'
setting.plugnmeet_analyticsurl.name: 'PlugNmeet URL analytics'
setting.plugnmeet_analyticsurl.description: 'Adres serwera plugNmeet do celów analitycznych'
setting.zoom_urlreports.name: 'Adres URL raportu Zoom'
setting.zoom_urlreports.description: 'Adres, pod którym przechowywane są raporty zoom'
setting.plugnmeet_urlreports.name: 'PlugNmeet reporting url'
setting.plugnmeet_urlreports.description: 'Adres, pod którym przechowywane są raporty plugNmeet'
setting.clickmeeting_urlreports.name: 'Adres URL raportowania ClickMeeting'
setting.clickmeeting_urlreports.description: 'Adres, pod którym przechowywane są raporty ClickMeeting'
setting.library_enabled.name: 'Biblioteka włączona'
setting.library_enabled.description: 'Biblioteka włączona'
setting.library_audio_local.name: 'Lokalny dźwięk biblioteki'
setting.library_audio_local.description: 'Lokalny dźwięk biblioteki'
setting.library_audio_path.name: 'Ścieżka audio biblioteki'
setting.library_audio_path.description: 'Ścieżka audio biblioteki'
setting.library_file_path.name: 'Ścieżka do pliku biblioteki'
setting.library_file_path.description: 'Ścieżka do pliku biblioteki'
setting.library_data_page_size.name: 'Rozmiar strony danych biblioteki'
setting.library_data_page_size.description: 'Rozmiar strony danych biblioteki'
setting.library_comments.name: 'Komentarze z biblioteki'
setting.library_comments.description: 'Komentarze z biblioteki'
setting.challenge_loops.name: 'Wyzwania związane z zapętlaniem'
setting.challenge_loops.description: 'Wyzwania związane z zapętlaniem'
setting.points_for_win.name: 'Punkty za zwycięstwo'
setting.points_for_win.description: 'Punkty za zwycięstwo'
setting.points_for_lose.name: 'Punkty za przegraną'
setting.points_for_lose.description: 'Punkty za przegraną'
setting.points_fortie.name: 'Punkty za remis'
setting.points_fortie.description: 'Punkty za remis'
setting.points_corrects.name: 'Punkty za prawidłowe'
setting.points_corrects.description: 'Punkty za prawidłowe'
setting.points_for_left.name: 'Pozostałe punkty'
setting.points_for_left.description: 'Pozostałe punkty'
setting.total_duels.name: 'Całkowita liczba pojedynków'
setting.total_duels.description: 'Całkowita liczba pojedynków'
setting.seconds_per_question.name: 'Sekundy na pytanie'
setting.seconds_per_question.description: 'Sekundy na pytanie'
setting.user_dni.name: 'Identyfikator użytkownika'
setting.user_dni.description: 'Pojawia się podczas tworzenia lub edytowania użytkownika'
setting.edit_code.name: 'Edycja kodu'
setting.edit_code.description: 'Jest to unikalny identyfikator użytkownika'
setting.stats_acumulative.name: 'Statystyki łączne'
setting.stats_acumulative.description: 'Dzieje się tak w przypadku, gdy statystyki mają się kumulować'
setting.maximo_fechas.name: 'Maksymalny zakres dat'
setting.maximo_fechas.description: 'Maksymalny zakres dat dla konsultacji'
setting.maximo_horas.name: 'Maksymalna liczba żądań na godzinę'
setting.maximo_horas.description: 'Maksymalna liczba żądań na godzinę'
setting.maximo_dia.name: 'Maksymalna liczba żądań dziennie'
setting.maximo_dia.description: 'Maksymalna liczba żądań dziennie'
setting.fundae.name: Fundae
setting.fundae.description: 'Jeśli ta opcja jest włączona, gdy połączenie jest publikowane, użytkownicy muszą wypełnić wszystkie niezbędne pola tabeli users_extra_fundae'
setting.margen_entrada.name: 'Domyślny margines wejścia'
setting.margen_entrada.description: 'Domyślny margines wejściowy, który jest używany w kodzie QR'
setting.margen_salida.name: 'Domyślny margines wyjściowy'
setting.margen_salida.description: 'Domyślny margines wyjściowy, który jest używany w kodzie QR'
setting.registrar_qr.name: 'Zarejestruj się za pomocą QR Session'
setting.registrar_qr.description: 'Jeśli ta opcja jest włączona, sesje będą rejestrowane za pomocą QR'
setting.maximo_alumnos.name: 'Maksymalna liczba uczniów w grupie'
setting.maximo_alumnos.description: 'Maksymalna liczba uczniów w grupie'
setting.min_score.name: 'Minimalny wynik pozytywny'
setting.min_score.description: 'Minimalny wynik pozytywny'
setting.types_action.name: 'Rodzaje działań'
setting.types_action.description: 'Rodzaje działań'
setting.materiales_convocatoria.name: 'Umożliwienie tworzenia materiałów w zaproszeniach do składania wniosków'
setting.materiales_convocatoria.description: 'Umożliwienie tworzenia materiałów w zaproszeniach do składania wniosków'
setting.tareas_convocatoria.name: 'Umożliwienie tworzenia zadań w zaproszeniu do składania wniosków'
setting.tareas_convocatoria.description: 'Umożliwienie tworzenia zadań w zaproszeniu do składania wniosków'
setting.minimo_minutos.name: 'Minimalny czas przestoju w minutach'
setting.minimo_minutos.description: 'Minimalny czas przestoju w minutach, dotyczy użytkowników znajdujących się na platformie'
setting.timezones.name: 'Strefy czasowe dozwolone w zaproszeniu do składania wniosków'
setting.timezones.description: 'Strefa czasowa, w której można skonfigurować zaproszenie do składania wniosków'
catalog.1.name: 'Rodzaje rozdziałów'
catalog.1.description: 'Konfiguracja rodzajów rozdziałów, które będą dostępne na platformie'
catalog.2.name: 'Rodzaje kursów'
catalog.2.description: 'Konfiguracja rodzajów kursów, które będą dostępne na platformie'
catalog.3.name: 'Kryteria zatwierdzenia'
catalog.3.description: 'Konfiguracja kryteriów zatwierdzania, które będą dostępne na platformie'
catalog.4.name: 'Powiadomienia dla opiekunów'
catalog.4.description: 'Konfiguracja powiadomień dla opiekunów, które będą dostępne na platformie'
catalog.5.name: 'Rodzaje dyplomów'
catalog.5.description: 'Konfiguracja rodzajów dyplomów, które będą dostępne na platformie'
catalog.6.name: 'Konfiguracja klienta w połączeniu'
catalog.6.description: 'Konfiguracja kroków wyświetlanych w zaproszeniu do składania wniosków'
catalog.7.name: 'Rodzaje monet'
catalog.7.description: 'Konfiguracja rodzajów walut, które będą dostępne na platformie'
catalog.8.name: 'Grupa konfiguracji'
catalog.8.description: 'Grupa konfiguracji, które będą dostępne na platformie'
catalog.9.name: Konfiguracje
catalog.9.description: 'Konfiguracje dla każdej grupy, które będą dostępne na platformie'
catalog.10.name: Firma
catalog.10.description: 'Firmy użytkowników, które będą dostępne na platformie'
catalog.11.name: 'Kategoria profesjonalna'
catalog.11.description: 'Profesjonalne kategorie użytkowników, które będą dostępne na platformie'
catalog.12.name: 'Centrum robocze użytkownika'
catalog.12.description: 'Miejsca pracy dla użytkowników, które będą dostępne na platformie'
catalog.13.name: 'Dział pracy użytkowników'
catalog.13.description: 'Działy pracy dla użytkowników, które będą dostępne na platformie'
catalog.14.name: 'Poziom badania użytkownika'
catalog.14.description: 'Poziomy nauki użytkowników, które będą dostępne na platformie'
catalog.15.name: 'Kroki dla różnych rodzajów kursów'
catalog.15.description: 'Konfiguracja kroków dla różnych typów kursów, które będą dostępne na platformie'
catalog.16.name: 'Rodzaje wirtualnych sal lekcyjnych'
catalog.16.description: 'Rodzaje wirtualnych sal lekcyjnych dla różnych rodzajów kursów, które będą dostępne na platformie'
catalog.17.name: 'Rodzaje identyfikacji'
catalog.17.description: 'Rodzaje identyfikacji dostępne na platformie'
nps_question.text.name: Tekst
nps_question.text.descripction: 'Przekaż nam swoją opinię'
setting.help.user.name: 'Dołącz pomoc pdf do menu użytkownika'
setting.help.user.description: 'Ta pomoc została stworzona specjalnie dla iberostar'
catalog.18.name: 'Sposoby prowadzenia rozmów twarzą w twarz'
catalog.18.description: 'Jest to szczególna potrzeba dla Iberostar'
setting.userPolicies_plataforma.name: 'Polityka prywatności'
setting.userPolicies_plataforma.description: 'Ta zmienna jest używana do włączenia modalu na interfejsie użytkownika, gdy użytkownik nie akceptuje polityki prywatności'
setting.course.tab.person.name: 'Zakładka ludzie - szczegóły kursu'
setting.course.tab.stats.name: "Szczegóły kursu w tabeli statystyk\n"
setting.course.tab.opinions.name: "Zakładka szczegółów kursu opinii\n"
setting.documentation.name: Dokumentacja
setting.documentation.description: 'Aktywuj moduł Dokumentacja w menu bocznym'
setting.user_company.name: Firmy
setting.user_company.description: 'Aktywuj moduł Companies w menu bocznym'
setting.pages.name: Stopka
setting.pages.description: 'Aktywuj stopkę w kampusie'
setting.lite_formation.name: 'Grupa szkoleniowa ds. statystyk ogólnych'
setting.lite_formation.description: 'Grupa szkoleniowa ds. statystyk ogólnych'
setting.lite_formation.formationHours.name: 'Godziny treningu'
setting.lite_formation.formationHours.description: 'Łączna liczba godzin szkoleniowych i średnia liczba godzin szkoleniowych na osobę'
setting.lite_formation.peopleWithCourses.name: 'Osoby z kursami'
setting.lite_formation.peopleWithCourses.description: 'Osoby aktualnie szkolące się i osoby, które ukończyły co najmniej jeden kurs'
setting.lite_formation.courseStartedAndFinished.name: 'Rozpoczęte, trwające i ukończone kursy'
setting.lite_formation.courseStartedAndFinished.description: 'Liczba rozpoczętych, trwających i ukończonych kursów'
setting.lite_formation.requiredCourses.name: 'Kursy obowiązkowe'
setting.lite_formation.requiredCourses.description: 'Obowiązkowe kursy przypisane do połączenia lub ścieżki'
setting.lite_formation.general.name: Ogólne
setting.lite_formation.general.description: Ogólne
setting.lite_formation.openedCourses.name: 'Kursy otwarte'
setting.lite_formation.openedCourses.description: 'Kursy dobrowolne'
setting.lite_formation.educativeStatus.name: 'Poziom wykształcenia'
setting.lite_formation.educativeStatus.description: 'Status szkolenia według poziomów punktowych'
setting.lite_formation.gamifiedPills.name: 'Pigułki gamifikowane'
setting.lite_formation.gamifiedPills.description: 'Liczba gamifikowanych rozdziałów, porażek i sukcesów w gamifikowanych quizach'
setting.lite_formation.gamifiedTest.name: 'Tabletki testowe'
setting.lite_formation.gamifiedTest.description: 'Używane testy grywalizacyjne oraz trafienia i nietrafienia według typu testu'
setting.lite_formation.peoplePerformance.name: 'Wydajność ludzi'
setting.lite_formation.peoplePerformance.description: 'Wydajność ludzi'
setting.lite_formation.coursesByStars.name: 'Kursy według wyniku'
setting.lite_formation.coursesByStars.description: 'Ocena kursów według gwiazdek'
setting.lite_formation.structureAndHotel.name: 'Mieszkania i hotele'
setting.lite_formation.structureAndHotel.description: 'Procent na grupę'
setting.lite_formation.schoolFinishedAndProgress.name: 'Szkoła Ukończono i w trakcie realizacji'
setting.lite_formation.schoolFinishedAndProgress.description: 'Szkoła z największą liczbą uczestników, kursy w toku i ukończone'
setting.lite_formation.coursesBySchool.name: 'Kursy według szkoły'
setting.lite_formation.coursesBySchool.description: 'Liczba kursów na kategorię'
setting.lite_formation.coursesByDepartment.name: 'Kursy według działów'
setting.lite_formation.coursesByDepartment.description: 'Tworzenie kursów przez dział'
setting.lite_formation.usersMoreActivesByCourses.name: 'Najaktywniejsi użytkownicy według kursu'
setting.lite_formation.usersMoreActivesByCourses.description: 'Najbardziej i najmniej aktywne osoby w ukończonych kursach'
setting.lite_evolution.name: 'Grupa ds. rozwoju statystyk ogólnych'
setting.lite_evolution.description: 'Grupa ds. rozwoju statystyk ogólnych'
setting.lite_evolution.trainedPerson.name: 'Osoby przeszkolone'
setting.lite_evolution.trainedPerson.description: 'Osoby, które ukończyły co najmniej jeden kurs'
setting.lite_evolution.startedCourses.name: 'Rozpoczęte kursy'
setting.lite_evolution.startedCourses.description: 'Rozpoczęte kursy'
setting.lite_evolution.proccessCourses.name: 'Kursy w toku'
setting.lite_evolution.proccessCourses.description: 'Kursy w toku'
setting.lite_evolution.finishedCourses.name: 'Ukończone kursy'
setting.lite_evolution.finishedCourses.description: 'Ukończone kursy'
setting.lite_evolution.segmentedHours.name: 'Segmentacja godzin'
setting.lite_evolution.userNewInPlatformThanFinishedOneCourse.name: 'Nowi użytkownicy, którzy ukończyli kurs'
setting.lite_evolution.userNewInPlatformThanFinishedOneCourse.description: 'Osoby nowe na platformie, które ukończyły co najmniej jeden kurs'
setting.lite_demography.name: 'Grupa demograficzna w statystyce ogólnej'
setting.lite_demography.description: 'Grupa demograficzna w statystyce ogólnej'
setting.lite_demography.usersBySexAndAge.name: 'Użytkownicy według płci i wieku'
setting.lite_demography.usersBySexAndAge.description: 'Użytkownicy według płci i wieku'
setting.lite_demography.ageDistribution.name: 'Rozkład wieku'
setting.lite_demography.ageDistribution.description: 'Rozkład wieku'
setting.lite_demography.deviceDistribution.name: 'Dystrybucja według urządzenia'
setting.lite_demography.deviceDistribution.description: 'Dystrybucja według urządzenia'
setting.lite_demography.usersByCountries.name: 'Dystrybucja według krajów'
setting.lite_demography.usersByCountries.description: 'Dystrybucja według krajów'
setting.lite_activity.name: 'Ogólne statystyki Grupa aktywności'
setting.lite_activity.description: 'Ogólne statystyki Grupa aktywności'
setting.lite_activity.activityInfo.name: 'Informacje o działalności'
setting.lite_activity.activityInfo.description: 'Osoby aktywne na portalu, osoby zarejestrowane, osoby, które zalogowały się co najmniej raz w ciągu ostatnich 30 dni, osoby dezaktywowane i osoby, które nigdy się nie logowały'
setting.lite_activity.accessDays.name: 'Dostęp według dni'
setting.lite_activity.accessDays.description: 'Dni dostępu'
setting.lite_activity.platformAccessByHours.name: 'Dostęp według platformy i czasu'
setting.lite_activity.platformAccessByHours.description: 'Czasy dostępu do platformy według dnia i godziny (mapa cieplna)'
setting.lite_activity.courseStartTime.name: 'Rozkład według godzin rozpoczęcia kursów'
setting.lite_activity.courseStartTime.description: 'Godziny rozpoczęcia kursu'
setting.lite_activity.courseEndTime.name: 'Rozkład według godzin ukończenia kursu'
setting.lite_activity.courseEndTime.description: 'Godziny ukończenia kursu (mapa cieplna)'
setting.lite_activity.coursesStartedVsFinished.name: 'Kursy rozpoczęte a kursy ukończone'
setting.lite_activity.coursesStartedVsFinished.description: 'Kursy rozpoczęte vs. kursy ukończone'
setting.lite_activity.usersMoreActivesByActivity.name: 'Najbardziej aktywni użytkownicy'
setting.lite_activity.usersMoreActivesByActivity.description: 'Najbardziej i najmniej aktywne osoby oraz ich czas spędzony na platformie'
setting.lite_itinerary.name: 'Trasy w grupie statystyk ogólnych'
setting.lite_itinerary.description: 'Trasy w grupie statystyk ogólnych'
setting.lite_itinerary.itinerariesStartedAndFinished.name: 'Zainicjowane i ukończone trasy'
setting.lite_itinerary.itinerariesStartedAndFinished.description: 'Rozpoczęte i ukończone trasy'
setting.lite_itinerary.itinerariesCompletedByCountries.name: 'Trasy ukończone według krajów'
setting.lite_itinerary.itinerariesCompletedByCountries.description: 'Trasy ukończone według krajów'
setting.survey.hide_empty_comment.name: 'Ukryj opinię z pustym komentarzem'
setting.survey.hide_empty_comment.description: 'Ankiety, ukryj nazwę, gdy komentarz jest pusty'
setting.survey.show_only_ratings.name: 'Ankieta, pokaż tylko nazwę kwalifikacji'
setting.survey.show_only_ratings.description: 'Ankieta pokazuje tylko nazwę kwalifikacji'
app.survey.post_nps.enabled.name: 'Nazwa publikacji ankiety nps włączona'
app.survey.post_nps.enabled.description: 'Nazwa badania publikacja aplikacji nps enabled'
setting.lite_evolution.segmentedHours.description: Godziny
setting.course.tab.person.description: 'Ludzie, szczegóły kursu'
setting.course.showDeactivatedCourses.name: 'Na karcie wyświetlane są nazwy nieaktywnych kursów'
setting.course.showDeactivatedCourses.description: 'Wyświetla nazwy dezaktywowanych kursów'
catalog.19.name: 'Administrator tłumaczeń'
catalog.19.description: 'Tłumaczenia administratora'
setting.lenguage.platform: 'Tłumaczenia Administratora'
setting.module.announcement.name: 'Zaproszenie do składania wniosków'
setting.module.announcement.description: 'Aktywuj moduł połączeń w podmenu kursów'
course.diploma.index: 'Spis treści'
setting.zip.day_available_until.name: 'Dostępne dni'
setting.zip.day_available_until.description: 'Liczba dostępnych dni przed automatycznym usunięciem pliku zip.'
catalog.20.name: 'Wywołanie dodatkowych pól'
course.diploma.filters: 'Aktywacja dodatkowych filtrów w raporcie dyplomowym'
setting.lenguage.platform.description: 'Języki dostępne w panelu administratora'
translations_admin.title1: 'Przydzielone szkolenie'
translations_admin.title2: 'Dodatkowe szkolenie'
translations_admin.title3: 'Przydzielone kursy'
translations_admin.title4: 'Kursy dobrowolne'
setting.course.tab.stats.description: 'Włączona aktywuje sekcję "Statystyki" na szczegółowym poziomie kursu.'
setting.course.tab.options.description: 'Włączona aktywuje sekcję "Opinie" na poziomie szczegółów kursu.'
course.diploma.index.description: 'Włączona sekcja "Dyplomy" jest aktywowana podczas tworzenia/modyfikowania kursu'
setting.use.filter_in_ranking.name: 'Korzystanie z filtrów w rankingu użytkowników'
setting.use.filter_in_ranking.description: 'Umożliwia wybranie z menu filtrów kategorii, z którymi użytkownik ma być porównywany. Jeśli ta opcja jest wyłączona, użytkownik będzie domyślnie porównywany ze wszystkimi filtrami dostępnymi na platformie'
setting.use.include_only_first_category_name: 'Pokaż tylko pierwszą kategorię filtra w rankingach'
setting.use.include_only_first_category_description: 'Jeśli jest aktywna, wyświetlane jest tylko pierwsze powiązanie użytkownika. W przeciwnym razie wyświetlane są wszystkie powiązane kategorie. Na przykład, jeśli kategorią jest "kraj", użytkownik może być powiązany zarówno z Hiszpanią, jak i Nikaraguą.'
setting.email_support_error.name: 'Poczta wsparcia dla błędów'
setting.email_support_error.description: 'Adresy e-mail, na które będą wysyłane incydenty platformy'
setting.export.task.slot_quantity.name: 'Liczba slotów zadań na użytkownika'
setting.export.task.slot_quantity.description: 'Liczba slotów dostępnych do przetwarzania zadań eksportu na użytkownika.'
setting.export.task.long_running_type_tasks.name: 'Rodzaje zadań długoterminowych'
setting.export.task.long_running_type_tasks.description: 'Lista typów zadań, które są uważane za długotrwałe w przypadku eksportu.'
setting.export.zip_task.slot_quantity.name: 'Liczba miejsc na zadania zip na użytkownika'
setting.export.zip_task.slot_quantity.description: 'Liczba gniazd dostępnych do przetwarzania zadań kompresji zip na użytkownika.'
setting.export.zip_task.long_running_type_tasks.name: 'Rodzaje długoterminowych zadań zip'
setting.export.zip_task.long_running_type_tasks.description: 'Lista typów zadań zip, które są uważane za długotrwałe.'
setting.export.task.user_pending_max_count_task.name: 'Maksymalna liczba oczekujących zadań na użytkownika'
setting.export.task.user_pending_max_count_task.description: 'Maksymalna liczba oczekujących zadań, które użytkownik może mieć w kolejce.'
setting.export.task.timeout.name: 'Limit czasu dla zadań'
setting.export.task.timeout.description: 'Maksymalny czas w sekundach przed uznaniem zadania eksportu za wygasłe.'
setting.export.zip_task.timeout.name: 'Limit czasu dla zadań zip'
setting.export.zip_task.timeout.description: 'Maksymalny czas w sekundach przed uznaniem zadania kompresji zip za wygasłe.'
setting.export.task.timeout_seconds.name: 'Czas oczekiwania dla zadań w stanie TIMEOUT'
setting.export.task.timeout_seconds.description: 'Maksymalny czas w sekundach, po którym zadanie w stanie TIMEOUT nie jest już uważane za uruchomione.'
type_diploma.novomatic.name: Novomatic
type_diploma.novomatic.description: 'Jest to spersonalizowany dyplom dla Novomatic'
app.announcement.managers.sharing.name: 'Umożliwienie tworzenia zadań w zaproszeniu do składania wniosków'
app.announcement.managers.sharing.description: 'Umożliwienie tworzenia zadań w zaproszeniu do składania wniosków'
