nps_question.text.description: '<PERSON><PERSON><PERSON>e uns Ihre Meinung'
nps_question.nps.description: 'Bewertung des Kurses'
type_course.teleformacion.name: E-learning
type_course.teleformacion.description: 'Für E-Learning-Kurse'
type_course.presencial.name: <PERSON><PERSON><PERSON><PERSON><PERSON>
type_course.presencial.description: '<PERSON><PERSON> vor Ort'
type_course.mixto.name: G<PERSON>ischt
type_course.mixto.description: 'Es handelt sich um eine Kombination aus E-Learning und Präsenzunterricht'
type_course.aula_virtual.name: 'Virtuelles Klassenzimmer'
type_course.aula_virtual.description: 'Der Unterricht findet per Videokonferenz statt'
alert_type_tutor.1.name: 'Die angerufene Person hat den Kurs nicht aufgerufen'
alert_type_tutor.1.description: 'Der Tutor erhält eine Benachrichtigung, wenn die geladene Person nicht auf den Kurs zugegriffen hat'
alert_type_tutor.2.name: '50 % der Gesprächszeit sind verstrichen und 25 % des Inhalts sind noch nicht abgeschlossen'
alert_type_tutor.2.description: 'Der Tutor erhält eine Warnmeldung, wenn 50 % der Gesprächszeit verstrichen sind und 25 % des Inhalts noch nicht abgeschlossen wurden'
alert_type_tutor.3.name: '80 % der Gesprächszeit sind verstrichen und 50 % des Inhalts sind noch nicht abgeschlossen'
alert_type_tutor.3.description: 'Der Tutor erhält eine Warnmeldung, wenn 80 % der Gesprächszeit verstrichen sind und 50 % des Inhalts noch nicht erledigt wurden'
alert_type_tutor.4.name: 'Es sind nur noch wenige Tage bis zum Ende des Aufrufs und der Kurs ist noch nicht abgeschlossen'
alert_type_tutor.4.description: 'Sie müssen die Anzahl der Tage beurteilen, die als wenige Tage gelten'
alert_type_tutor.5.name: 'Die angerufene Person hat den Kurs abgeschlossen, aber nicht auf die Umfrage geantwortet.'
alert_type_tutor.5.description: 'Wenn die Plattform über Umfragen verfügt, erhält der Tutor eine Benachrichtigung, wenn die aufgerufene Person den Kurs abgeschlossen, aber die Umfrage nicht beantwortet hat'
alert_type_tutor.6.name: 'Die Person hat den Kurs abgeschlossen, aber das Diplom noch nicht heruntergeladen'
alert_type_tutor.6.description: 'Der Tutor erhält eine Warnmeldung, wenn die vorgeladene Person den Kurs abgeschlossen, aber das Zeugnis nicht heruntergeladen hat.'
announcement_configuration_type.temporalizacion.name: Timing
announcement_configuration_type.temporalizacion.description: 'Um die Überwachung des Kurses zu erleichtern, werden wir jedem Block von Inhalten und Aktivitäten eine Zeit zuordnen, so dass wir feststellen können, welche der Teilnehmer in einem angemessenen Tempo arbeiten oder im Trainingsprozess zurückbleiben'
announcement_configuration_type.curso_bonificado.name: 'Subventionierter Kurs'
announcement_configuration_type.curso_bonificado.description: 'Bei den subventionierten Kursen handelt es sich um Kurse, die von der Dreigliedrigen Stiftung durchgeführt und von den Unternehmen durch Sozialversicherungsbeiträge finanziert werden.'
announcement_configuration_type.chat.name: Chat
announcement_configuration_type.chat.description: 'Der Chat ist ein synchrones Kommunikationswerkzeug, mit dem die Kursteilnehmer in Echtzeit über Textnachrichten miteinander kommunizieren können.'
announcement_configuration_type.notificaciones.name: Benachrichtigungen
announcement_configuration_type.notificaciones.description: 'Benachrichtigungen sind Nachrichten, die an Kursteilnehmer gesendet werden, um sie über wichtige Neuigkeiten oder Ereignisse zu informieren.'
announcement_configuration_type.mensajeria.name: Nachrichtenübermittlung
announcement_configuration_type.mensajeria.description: 'Ein Messaging-System ist ein Kommunikationssystem, mit dem Kursteilnehmer private Nachrichten senden und empfangen können.'
announcement_configuration_type.foros.name: Foren
announcement_configuration_type.foros.description: 'Ein Forum ist ein asynchroner Kommunikationsraum, in dem Kursteilnehmer Nachrichten zu einem bestimmten Thema austauschen können.'
announcement_configuration_type.diploma.name: Diplom
announcement_configuration_type.diploma.description: 'Diplome sind Bescheinigungen, die den Teilnehmern eines Kurses als Nachweis für den Abschluss verliehen werden.'
announcement_configuration_type.tutor_alerts.name: 'Tutorenwarnungen aktivieren'
announcement_configuration_type.tutor_alerts.description: 'Alerts sind Nachrichten, die an den Tutor eines Kurses geschickt werden, um ihn über wichtige Neuigkeiten oder Ereignisse zu informieren.'
announcement_configuration_type.encuesta_satisfaccion.name: Zufriedenheitsumfrage
announcement_configuration_type.encuesta_satisfaccion.description: 'Zufriedenheitsumfragen sind Fragebögen, die den Kursteilnehmern zugeschickt werden, um herauszufinden, was sie über den Kurs denken.'
announcement_configuration_type.finalizar_convocatoria.name: 'Der Kurs bleibt nach dem Ende des Gesprächs aktiv'
announcement_configuration_type.finalizar_convocatoria.description: 'Der Nutzer kann auch nach Beendigung des Kurses auf die Kursinhalte zugreifen.'
announcement_configuration_type.firma_digital.name: 'Digitale Unterschrift'
announcement_configuration_type.firma_digital.description: 'Eine digitale Unterschrift ist erforderlich, um die Teilnahme an einer Präsenzveranstaltung zu bestätigen.'
announcement_configuration_type.gestion_costes.name: Kostenmanagement
announcement_configuration_type.gestion_costes.description: 'Die Kostenverwaltung ermöglicht es Gruppen, die Kosten des Anrufs anzugeben.'
announcement_configuration_type.EMAIL_NOTIFICATION_ON_ANNOUNCEMENT.name: EMAIL_BENACHRICHTIGUNG_BEI_ANKÜNDIGUNG
announcement_configuration_type.EMAIL_NOTIFICATION_ON_ANNOUNCEMENT.description: 'Aktivieren Sie E-Mail-Benachrichtigungen.'
announcement_configuration_type.NOTIFICATION_ON_ANNOUNCEMENT.name: BENACHRICHTIGUNG_BEI_ANKÜNDIGUNG
announcement_configuration_type.NOTIFICATION_ON_ANNOUNCEMENT.description: 'Normale Benachrichtigungen einschalten'
announcement_configuration_type.objetivos_contenidos.name: 'Zielsetzung und Inhalt'
announcement_configuration_type.objetivos_contenidos.description: 'Die Ziele und Inhalte des Kurses werden in das Diplom aufgenommen.'
announcement_configuration_type.dni.name: Dni
announcement_configuration_type.dni.description: 'Die ID des Schülers wird auf dem Abschlusszeugnis vermerkt'
announcement_configuration_type.template_excel.name: 'Einschreibeformular Excel'
announcement_configuration_type.template_excel.description: 'Diese Vorlage wird für die Immatrikulation von Studenten verwendet, wobei der HRBP-Code anstelle der DNI verwendet wird.'
announcement_configuration_type.report_zip.name: ZIP
announcement_configuration_type.report_zip.description: 'Ermöglichen Sie es dem Tutor, Gruppenberichte im ZIP-Format herunterzuladen'
announcement_criteria.1.name: 'Komplette Kapitel'
announcement_criteria.1.description: '% der Kapitel, die zur Erfüllung des Kriteriums des Bestehens der Aufforderung beitragen müssen.'
announcement_criteria.2.name: 'Erledigung von Aufgaben'
announcement_criteria.2.description: 'Prozentsatz der Aufgaben, die zur Erfüllung des Kriteriums des Bestehens der Aufforderung beitragen müssen.'
announcement_criteria.3.name: 'Maximale Ausfallzeit'
announcement_criteria.3.description: 'Sobald diese Anzahl an Minuten Bildschirmzeit ohne Inaktivität erreicht ist, wird die Zeitzählung angehalten, bis die Aktivität des Benutzers erneut erkannt wird.'
announcement_criteria.4.name: 'Abschluss der Aktivitäten'
announcement_criteria.4.description: 'Sobald diese Anzahl an Minuten Bildschirmzeit ohne Inaktivität erreicht ist, wird die Zeitzählung angehalten, bis die Aktivität des Benutzers erneut erkannt wird.'
announcement_criteria.5.name: 'Zeit abgeschlossen'
announcement_criteria.5.description: '% der Stunden, die erforderlich sind, um zum Bestehenskriterium des Anrufs beizutragen.'
announcement_step_creation.ANNOUNCEMENT_COURSE.description: 'Erster Schritt bei der Erstellung der Aufforderung'
announcement_step_creation.ANNOUNCEMENT_GENERAL_INFO.description: 'Zweiter Schritt der Anruferstellung, bei dem allgemeine Informationen eingegeben werden'
announcement_step_creation.ANNOUNCEMENT_BONUS.description: 'Dieser Schritt hängt davon ab, ob der Kunde den Bonus aktiviert hat'
announcement_step_creation.ANNOUNCEMENT_STUDENTS.description: 'Hier werden die Schüler dem Anruf hinzugefügt, die einer Gruppe zugeordnet werden können'
announcement_step_creation.ANNOUNCEMENT_GROUPS.description: 'In diesem Schritt werden die im vorherigen Schritt erstellten Schülergruppen konfiguriert'
announcement_step_creation.ANNOUNCEMENT_COMMUNICATION.description: 'In diesem Schritt wird die Umfrage konfiguriert, die an die Schüler gesendet werden soll'
announcement_step_creation.ANNOUNCEMENT_SURVEY.description: 'In diesem Schritt wird die Umfrage konfiguriert, die an die Schüler gesendet werden soll'
announcement_step_creation.ANNOUNCEMENT_CERTIFICATE.description: 'In diesem Schritt werden die auf der Plattform verfügbaren Diplome angezeigt, aus denen der Kunde das gewünschte auswählen kann'
announcement_step_creation.ANNOUNCEMENT_ALERTS.description: 'Diese Alarme sind spezielle Alarme, die den Tutor über die Ereignisse des Anrufs informieren'
class_room_virtual.zoom.name: Vergrößern
class_room_virtual.zoom.description: 'Online-Webkonferenzplattform, die hochauflösende Videoanrufe mit den Funktionen Desktop-Sharing, Whiteboard, Chat, Konferenzaufzeichnung, Dokumentenaustausch und Zugriff von überall aus ermöglicht, da sie für mobile Geräte verfügbar ist.'
class_room_virtual.clickmeeting.name: ClickMeeting
class_room_virtual.clickmeeting.description: 'Die ClickMeeting-Plattform ist eine der benutzerfreundlichsten Webinar-Oberflächen auf dem Markt und bietet zahlreiche flexible Anpassungsmöglichkeiten'
class_room_virtual.jitsi.name: Jitsi
class_room_virtual.jitsi.description: 'Open-Source-Lösung für Videokonferenzen mit verschlüsselten Verbindungen und verfügbar für verschiedene Betriebssysteme'
class_room_virtual.plugnmeet.name: PlugnMeet
class_room_virtual.plugnmeet.description: 'Einfach zu integrierende und hochgradig anpassbare Open-Source-Videokonferenzsoftware'
configuration_cliente_announcement.COMMUNICATION.description: 'Dadurch kann die Kommunikation innerhalb des Gesprächs ermöglicht werden'
configuration_cliente_announcement.CERTIFICATE.description: 'Dies ermöglicht das Herunterladen von Diplomen innerhalb des Anrufs'
configuration_cliente_announcement.SURVEY.description: 'Auf diese Weise können die Umfragen aktiviert werden, um sie später im Gespräch verfügbar zu machen'
configuration_cliente_announcement.ALERT.description: 'Dies ermöglicht die Aktivierung von Warnungen, die in den Bereich der Warnungen des Tutors übernommen werden können'
configuration_cliente_announcement.TEMPORALIZATION.description: 'Ermöglicht die zeitliche Einordnung von Kapiteln innerhalb einer Aufforderung zur Einreichung von Vorschlägen'
configuration_cliente_announcement.BONIFICATION.description: 'Bonus für den Aufruf, insbesondere für den Aufruf der dreigliedrigen Stiftung'
configuration_cliente_announcement.ACCESS_CONTENT.description: 'Ermöglicht den Zugriff auf den Inhalt des Anrufs, sobald dieser beendet ist'
configuration_cliente_announcement.DIGITAL_SIGNATURE.description: 'Ermöglicht die Aktivierung der digitalen Signatur in der Aufforderung zur Einreichung von Bewerbungen, insbesondere in Präsenzveranstaltungen'
configuration_cliente_announcement.COST.description: 'Ermöglichen Sie es den Kunden, die Kosten dem Anruf zuzuordnen.'
configuration_cliente_announcement.NOTIFICATION_ACTIVATE_ANNOUNCEMENT.description: 'Benachrichtigungen bei Rufauslösung (E-Mail, Frontbenachrichtigung)'
configuration_cliente_announcement.CONFIGURATION_IBEROSTAR.description: 'Diese Konfiguration wurde speziell für Iberostar-Kunden entwickelt, um die Geldflüsse nicht zu beeinträchtigen'
configuration_cliente_announcement.REPORT.description: 'Ermöglichung der Berichterstattung in Aufforderungen zur Einreichung von Vorschlägen'
type_course_announcement_step_creation.seleccionar_curso.name: 'Kurs auswählen'
type_course_announcement_step_creation.seleccionar_curso.description: 'Wählen Sie den Kurs aus, für den der Aufruf erstellt werden soll'
type_course_announcement_step_creation.convocatoria.name: 'Aufruf zur Einreichung von Bewerbungen'
type_course_announcement_step_creation.convocatoria.description: 'Informationen über die Aufforderung zur Einreichung von Vorschlägen'
type_course_announcement_step_creation.bonificacion.name: Bonus
type_course_announcement_step_creation.bonificacion.description: 'Informationen über die Aufforderung zur Einreichung von Vorschlägen'
type_course_announcement_step_creation.alumnado.name: Alumni
type_course_announcement_step_creation.alumnado.description: 'Studenten werden zum Kurs hinzugefügt'
type_course_announcement_step_creation.grupos.name: Gruppen
type_course_announcement_step_creation.grupos.description: 'Die Gruppeninformationen sind detailliert und der Tutor wird ebenfalls hinzugefügt'
type_course_announcement_step_creation.comunicacion.name: Kommunikation
type_course_announcement_step_creation.comunicacion.description: 'Dieser Schritt hängt davon ab, ob der Kunde die Kommunikation aktiviert hat oder nicht.'
type_course_announcement_step_creation.encuesta.name: Umfrage
type_course_announcement_step_creation.encuesta.description: 'Dieser Schritt hängt davon ab, ob der Kunde die Umfrage aktiviert hat oder nicht.'
type_course_announcement_step_creation.diploma.name: Diplom
type_course_announcement_step_creation.diploma.description: 'Dieser Schritt hängt davon ab, ob der Kunde das Diplom aktiviert hat oder nicht.'
type_course_announcement_step_creation.alertas.name: Warnungen
type_course_announcement_step_creation.alertas.description: 'Dies kann von der Konfiguration des Kunden abhängen.'
type_diploma.easylearning.name: Standard
type_diploma.easylearning.description: 'Es ist das Diplom des Kundenunternehmens'
type_diploma.fundae.name: Fundae
type_diploma.fundae.description: 'Es ist das Fundae-Diplom'
type_diploma.hobetuz.name: Hobetuz
type_diploma.hobetuz.description: 'Es ist das Hobetuz-Diplom'
type_money.euro.name: Euro
type_money.euro.country: Spanien
type_money.dolar_estadounidense.name: US-Dollar
type_money.dolar_estadounidense.country: 'Vereinigte Staaten'
section_default_front.mi_formacion.name: 'Meine Ausbildung'
section_default_front.mi_formacion.description: 'Innerhalb der zugewiesenen Ausbildung finden Sie alle Kurse, die Ihnen zugewiesen wurden.'
section_default_front.formacion_adicional.name: 'Zusätzliche Ausbildung'
section_default_front.formacion_adicional.description: 'In diesem Abschnitt finden Sie alle offenen Campus-Kurse.'
section_default_front.formacion_asignada.name: 'Zugewiesene Ausbildung'
section_default_front.formacion_asignada.description: 'In diesem Bereich finden Sie alle Ihnen zugewiesenen Kurse.'
setting.multi_idioma.name: Mehrsprachig
setting.multi_idioma.description: 'Es bietet eine mehrsprachige Schnittstelle'
setting.default_lenguage.name: Standardsprache
setting.default_lenguage.description: 'Standardsprache der Benutzeroberfläche des Systems'
setting.languages.name: Sprache
setting.languages.description: 'In der Anwendung verfügbare Sprachen'
setting.registro_libre.name: 'Kostenlose Benutzerregistrierung'
setting.registro_libre.description: 'Modus, der es dem Nutzer ermöglicht, sich nach Ausfüllen des entsprechenden Formulars frei auf der Plattform zu registrieren.'
setting.opinion_plataforma.name: 'Plattform Stellungnahmen'
setting.opinion_plataforma.description: 'Plattform Stellungnahmen'
setting.validacion_automatica.name: 'Automatische Überprüfung der Benutzerregistrierung'
setting.validacion_automatica.description: 'Automatische Überprüfung der Benutzerregistrierung'
setting.filtros_plataforma.name: 'Filter auf der Plattform'
setting.filtros_plataforma.description: 'Damit können Sie die Filter auf der Plattform aktivieren oder deaktivieren'
setting.itinearios_plataforma.name: 'Reiserouten auf der Plattform'
setting.itinearios_plataforma.description: 'Sie dient dazu, die Routen auf der Plattform zu aktivieren oder zu deaktivieren'
setting.seccion_cursos.name: 'Kursabschnitte [FRONT]'
setting.seccion_cursos.description: 'Kursabschnitte [FRONT],'
setting.set_points_course.name: 'Punkte für den Kurs setzen'
setting.set_points_course.description: 'Dies wird verwendet, um bei der Erstellung oder Bearbeitung von Kursen Punkte zu vergeben, insbesondere für den E-Learning-Kurs'
setting.default_points_course.name: 'Standardpunkte für den Kurs'
setting.default_points_course.description: 'Bei Spielformeln werden halbe Punkte vergeben, wenn das Kapitel kein Spiel ist'
setting.documentation_course.name: 'Allgemeine Informationen'
setting.documentation_course.description: 'Aktivieren Sie froala, um die allgemeinen Kursinformationen in Schritt 2 des Kurses hinzuzufügen'
setting.open_course.name: 'Offene Kurse'
setting.open_course.description: 'Auf diese Weise können der Plattform offene Kurse oder zusätzliche Schulungen hinzugefügt werden'
setting.client_id.name: 'Vimeo Kunden-ID'
setting.client_id.description: 'Dies ist die Kennung des Vimeo-Kunden'
setting.client_secret.name: 'Heimlicher Vimeo-Client'
setting.client_secret.description: 'Heimlicher Vimeo-Client'
setting.access_token.name: Zugangstoken
setting.access_token.description: Zugangstoken
setting.user_id.name: Benutzer-ID
setting.user_id.description: 'Vimeo registrierte Benutzer-ID'
setting.project_id.name: 'Ordner für Videokapitel'
setting.project_id.description: 'Dies ist die Kennung, unter der die Ressourcen der Videokapitel gehostet werden'
setting.project_id_resource_course.name: 'Ressourcenkit Materialien (Aufforderung zur Einreichung von Vorschlägen)'
setting.project_id_resource_course.description: 'Dies ist die Kennung des Ordners, in dem die Videos zum Kursmaterial und zum Aufruf gespeichert sind'
setting.project_id_task_course.name: Aufgaben-Ressourcenordner
setting.project_id_task_course.description: 'Dies ist die Kennung des Ordners, in dem die Videos zu den Kursaufgaben und zum Aufruf gespeichert sind'
setting.project_id_video_Quiz.name: Videoquiz-Ressourcenordner
setting.project_id_video_Quiz.description: 'Es ist der Bezeichner des Ordners, in dem die Videos zum Videoquizspiel gespeichert sind'
setting.project_id_Roleplay.name: 'Ressourcenkit für Rollenspiele'
setting.project_id_Roleplay.description: 'Identifiziert für videoartige Ressourcen im Rollenspiel'
setting.upload_sudomain.name: 'Hochladen auf die Subdomain'
setting.upload_sudomain.description: 'Diese Variable wird für das Hochladen von Videos und SCORM-Dateien verwendet, wodurch die Beschränkungen von Cloudflare überwunden werden können'
setting.from_email.name: 'Von E-Mail'
setting.from_email.description: 'Sie ist der Ursprung der von der Plattform gesendeten E-Mails'
setting.from_name.name: 'Vom Namen der Plattform'
setting.from_name.description: 'Der Name der Plattform, der in den E-Mails und Diplomen angegeben ist'
setting.from_cif.name: 'Vom CIF'
setting.from_cif.description: 'Umsatzsteuer-Identifikationsnummer des Unternehmens'
setting.email_support.name: E-Mail-Unterstützung
setting.email_support.description: 'Diese E-Mails werden zum Versenden von Support-Benachrichtigungen verwendet'
setting.email_support_register.name: 'Empfang des E-Mail-Administrators'
setting.email_support_register.description: 'Dies ist eine E-Mail, die verwendet wird, um die Anfragen für die Registrierung auf der Plattform zu erhalten'
setting.news.name: Nachrichten
setting.news.description: Nachrichten
setting.foro.name: Forum
setting.foro.description: Forum
setting.desafios.name: Herausforderungen
setting.desafios.description: Herausforderungen
setting.secciones.name: Rubriken
setting.secciones.description: 'Mit dieser Variable können Sie konfigurieren, ob die Abschnitte in der Frontansicht angezeigt werden'
setting.encuestas.name: Erhebungen
setting.encuestas.description: Erhebungen
setting.active_cron_exports.name: 'Aktive Cron-Exporte'
setting.active_cron_exports.description: 'Aktive Cron-Exporte, die im Allgemeinen für den Export von Daten aus der Plattform verwendet werden'
setting.gender_excel.name: 'Genero excel'
setting.gender_excel.description: 'Dient zum Hinzufügen des Geschlechts in den Export-Extrakten'
setting.code.name: Code
setting.code.description: 'Codefeld beim Export der Kursstatistik anzeigen'
setting.finished_chapters.name: 'Abgeschlossene Kapitel'
setting.finished_chapters.description: 'abgeschlossene Kapitel im Kurs-Statistik-Export anzeigen'
setting.zoom_cliente_id.name: 'Kunden-ID vergrößern'
setting.zoom_cliente_id.description: 'Zoom-Client-ID - für die Nutzung der Zoom-API erforderlich'
setting.zoom_cliente_secret.name: 'Zoom Client Geheimnis'
setting.zoom_cliente_secret.description: 'zoom-Client-Schlüssel - wird für die Nutzung der Zoom-API benötigt'
setting.zoom_account_id.name: 'Zoom Konto-ID'
setting.zoom_account_id.description: 'Zoom-Kundenkontonummer - erforderlich für die Nutzung der Zoom-API'
setting.zoom_email.name: 'E-Mail vergrößern'
setting.zoom_email.description: 'zoom-Client-Mail - für die Verwendung der Zoom-API erforderlich'
setting.clickmeeting_api_key.name: 'ClickMeeting API-Schlüssel'
setting.clickmeeting_api_key.description: 'ClickMeeting-Client-ID - erforderlich für die Verwendung der ClickMeeting-API'
setting.clikmeeting_dirbase.name: ClickMeeting-Basisverzeichnis
setting.clikmeeting_dirbase.description: ClickMeeting-Server-Adresse
setting.clikmeeting_events_paralel.name: 'ClickMeeting Nebenveranstaltungen'
setting.clikmeeting_events_paralel.description: 'Anzahl der vertraglich vereinbarten Nebenveranstaltungen'
setting.plugnmeet_serverurl.name: 'PlugNmeet Server Url'
setting.plugnmeet_serverurl.description: PlugNmeet-Server-Adresse
setting.plugnmeet_api_key.name: 'PlugNmeet API-Schlüssel'
setting.plugnmeet_api_key.description: 'PlugNmeet Kunden-ID'
setting.plugnmeet_secret.name: 'PlugNmeet geheimer Schlüssel'
setting.plugnmeet_secret.description: 'PlugNmeet Client-Schlüssel'
setting.plugnmeet_analyticsurl.name: 'PlugNmeet URL-Analytik'
setting.plugnmeet_analyticsurl.description: 'Adresse des plugNmeet-Servers für die Analytik'
setting.zoom_urlreports.name: 'Zoom Bericht Url'
setting.zoom_urlreports.description: 'Adresse, unter der die Zoomberichte gespeichert werden'
setting.plugnmeet_urlreports.name: PlugNmeet-Berichts-Url
setting.plugnmeet_urlreports.description: 'Adresse, unter der plugNmeet-Berichte gespeichert werden'
setting.clickmeeting_urlreports.name: ClickMeeting-Berichts-URL
setting.clickmeeting_urlreports.description: 'Adresse, unter der ClickMeeting-Berichte gespeichert werden'
setting.library_enabled.name: 'Bibliothek aktiviert'
setting.library_enabled.description: 'Bibliothek aktiviert'
setting.library_audio_local.name: 'Lokales Audio der Bibliothek'
setting.library_audio_local.description: 'Lokales Audio der Bibliothek'
setting.library_audio_path.name: 'Audiopfad der Bibliothek'
setting.library_audio_path.description: 'Audiopfad der Bibliothek'
setting.library_file_path.name: 'Pfad der Bibliotheksdatei'
setting.library_file_path.description: 'Pfad der Bibliotheksdatei'
setting.library_data_page_size.name: 'Seitengröße der Bibliotheksdaten'
setting.library_data_page_size.description: 'Seitengröße der Bibliotheksdaten'
setting.library_comments.name: 'Kommentare aus der Bibliothek'
setting.library_comments.description: 'Kommentare aus der Bibliothek'
setting.challenge_loops.name: 'Herausforderungen beim Looping'
setting.challenge_loops.description: 'Herausforderungen beim Looping'
setting.points_for_win.name: 'Punkte für den Sieg'
setting.points_for_win.description: 'Punkte für den Sieg'
setting.points_for_lose.name: 'Punkte für das Verlieren'
setting.points_for_lose.description: 'Punkte für das Verlieren'
setting.points_fortie.name: 'Punkte für ein Unentschieden'
setting.points_fortie.description: 'Punkte für ein Unentschieden'
setting.points_corrects.name: 'Punkte für richtige'
setting.points_corrects.description: 'Punkte für richtige'
setting.points_for_left.name: 'Punkte links'
setting.points_for_left.description: 'Punkte links'
setting.total_duels.name: 'Gesamtzahl der Duelle'
setting.total_duels.description: 'Gesamtzahl der Duelle'
setting.seconds_per_question.name: 'Sekunden pro Frage'
setting.seconds_per_question.description: 'Sekunden pro Frage'
setting.user_dni.name: Benutzer-ID
setting.user_dni.description: 'Dies erscheint beim Erstellen oder Bearbeiten eines Benutzers'
setting.edit_code.name: 'Bearbeitung des Codes'
setting.edit_code.description: 'Sie ist eine eindeutige Kennung für den Benutzer'
setting.stats_acumulative.name: 'Kumulative Statistik'
setting.stats_acumulative.description: 'Dies ist der Fall, wenn die Statistiken kumulativ sein sollen'
setting.maximo_fechas.name: 'Maximaler Datumsbereich'
setting.maximo_fechas.description: 'Maximaler Zeitrahmen für Konsultationen'
setting.maximo_horas.name: 'Maximale Anfragen pro Stunde'
setting.maximo_horas.description: 'Maximale Anfragen pro Stunde'
setting.maximo_dia.name: 'Maximale Anfragen pro Tag'
setting.maximo_dia.description: 'Maximale Anfragen pro Tag'
setting.fundae.name: Fundae
setting.fundae.description: 'Wenn dies bei der Veröffentlichung eines Aufrufs aktiviert ist, müssen die Benutzer alle erforderlichen Felder in der Tabelle users_extra_fundae ausfüllen'
setting.margen_entrada.name: Standard-Eingabemarge
setting.margen_entrada.description: 'Standard-Eingabebereich, der im QR-Code verwendet wird'
setting.margen_salida.name: Standard-Ausgangsspanne
setting.margen_salida.description: 'Standardausgangsrand, der im QR-Code verwendet wird'
setting.registrar_qr.name: 'Registrieren mit QR Session'
setting.registrar_qr.description: 'Wenn dies aktiviert ist, werden die Sitzungen mit QR protokolliert'
setting.maximo_alumnos.name: 'Maximale Anzahl von Schülern pro Gruppe'
setting.maximo_alumnos.description: 'Maximale Anzahl von Schülern pro Gruppe'
setting.min_score.name: 'Mindestpunktzahl zum Bestehen'
setting.min_score.description: 'Mindestpunktzahl zum Bestehen'
setting.types_action.name: 'Arten von Maßnahmen'
setting.types_action.description: 'Arten von Maßnahmen'
setting.materiales_convocatoria.name: 'Ermöglichung der Erstellung von Materialien in Aufforderungen zur Einreichung von Vorschlägen'
setting.materiales_convocatoria.description: 'Ermöglichung der Erstellung von Materialien in Aufforderungen zur Einreichung von Vorschlägen'
setting.tareas_convocatoria.name: 'Ermöglicht die Erstellung von Aufgaben in einer Aufforderung zur Einreichung von Vorschlägen'
setting.tareas_convocatoria.description: 'Ermöglicht die Erstellung von Aufgaben in einer Aufforderung zur Einreichung von Vorschlägen'
setting.minimo_minutos.name: 'Minimale Ausfallzeit in Minuten'
setting.minimo_minutos.description: 'Minimale Ausfallzeit in Minuten, gilt für Nutzer, die sich auf der Plattform befinden'
setting.timezones.name: 'Zulässige Zeitzonen in der Aufforderung zur Einreichung von Vorschlägen'
setting.timezones.description: 'Zeitzone, in der die Aufforderung zur Einreichung von Vorschlägen konfiguriert werden kann'
catalog.1.name: 'Arten von Kapiteln'
catalog.1.description: 'Konfiguration der Arten von Kapiteln, die auf der Plattform verfügbar sein werden'
catalog.2.name: 'Arten von Kursen'
catalog.2.description: 'Konfiguration der Kursarten, die auf der Plattform verfügbar sein werden'
catalog.3.name: 'Kriterien für die Zulassung'
catalog.3.description: 'Konfiguration der Zulassungskriterien, die auf der Plattform verfügbar sein werden'
catalog.4.name: Tutorenwarnungen
catalog.4.description: 'Konfiguration von Warnmeldungen für Tutoren, die auf der Plattform verfügbar sein werden'
catalog.5.name: 'Arten von Diplomen'
catalog.5.description: 'Konfiguration der Arten von Diplomen, die auf der Plattform verfügbar sein werden'
catalog.6.name: 'Client-Konfiguration beim Aufruf'
catalog.6.description: 'Konfiguration der Schritte, die in der Aufforderung zur Einreichung von Vorschlägen angezeigt werden sollen'
catalog.7.name: 'Arten von Münzen'
catalog.7.description: 'Konfiguration der Arten von Währungen, die auf der Plattform verfügbar sein werden'
catalog.8.name: 'Gruppe von Konfigurationen'
catalog.8.description: 'Gruppe von Konfigurationen, die auf der Plattform verfügbar sein werden'
catalog.9.name: Konfigurationen
catalog.9.description: 'Konfigurationen pro Gruppe, die auf der Plattform verfügbar sein werden'
catalog.10.name: Unternehmen
catalog.10.description: 'Nutzerunternehmen, die auf der Plattform verfügbar sein werden'
catalog.11.name: Berufskategorie
catalog.11.description: 'Professionelle Nutzerkategorien, die auf der Plattform verfügbar sein werden'
catalog.12.name: Benutzerarbeitsplatz
catalog.12.description: 'Arbeitsplätze für Nutzer, die auf der Plattform verfügbar sein werden'
catalog.13.name: 'Abteilung Benutzerarbeit'
catalog.13.description: 'Arbeitsbereiche für Nutzer, die auf der Plattform verfügbar sein werden'
catalog.14.name: 'Benutzer Studie Ebene'
catalog.14.description: 'Die Lernstufen der Nutzer, die auf der Plattform verfügbar sein werden'
catalog.15.name: 'Schritte für die verschiedenen Arten von Kursen'
catalog.15.description: 'Konfiguration der Schritte für die verschiedenen Arten von Kursen, die auf der Plattform verfügbar sein werden'
catalog.16.name: 'Arten von virtuellen Klassenzimmern'
catalog.16.description: 'Arten von virtuellen Klassenzimmern für die verschiedenen Arten von Kursen, die auf der Plattform verfügbar sein werden'
catalog.17.name: 'Arten der Identifizierung'
catalog.17.description: 'Auf der Plattform verfügbare Arten der Identifizierung'
nps_question.text.name: Text
nps_question.text.descripction: 'Geben Sie uns Ihre Meinung'
setting.help.user.name: 'Hilfe-PDF in das Benutzermenü einfügen'
setting.help.user.description: 'Dieses Hilfsmittel wurde speziell für iberostar entwickelt'
catalog.18.name: 'Modalitäten für persönliche Gespräche'
catalog.18.description: 'Dies ist ein besonderer Bedarf für Iberostar'
setting.userPolicies_plataforma.name: Datenschutzbestimmungen
setting.userPolicies_plataforma.description: 'Diese Variable wird verwendet, um ein Modal auf dem Frontend zu aktivieren, wenn der Benutzer die Datenschutzrichtlinie nicht akzeptiert'
setting.course.tab.person.name: 'Registerkarte Menschen Kursdetails'
setting.course.tab.stats.name: "Statistik-Tabelle Kursdetails\n"
setting.course.tab.opinions.name: "Registerkarte der Meinungen Kursdetails\n"
setting.documentation.name: Dokumentation
setting.documentation.description: 'Aktivieren Sie das Modul Dokumentation im Seitenmenü'
setting.user_company.name: Unternehmen
setting.user_company.description: 'Aktivieren Sie das Modul Firmen im Seitenmenü'
setting.pages.name: Fußzeile
setting.pages.description: 'Aktivieren Sie die Fußzeile auf dem Campus'
setting.lite_formation.name: 'Ausbildungsgruppe Allgemeine Statistik'
setting.lite_formation.description: 'Ausbildungsgruppe Allgemeine Statistik'
setting.lite_formation.formationHours.name: Ausbildungsstunden
setting.lite_formation.formationHours.description: 'Gesamtzahl der Ausbildungsstunden und durchschnittliche Anzahl der Ausbildungsstunden pro Person'
setting.lite_formation.peopleWithCourses.name: 'Menschen mit Kursen'
setting.lite_formation.peopleWithCourses.description: 'Personen, die sich in der Ausbildung befinden und Personen, die mindestens einen Kurs abgeschlossen haben'
setting.lite_formation.courseStartedAndFinished.name: 'Begonnene, laufende und abgeschlossene Kurse'
setting.lite_formation.courseStartedAndFinished.description: 'Anzahl der begonnenen, laufenden und abgeschlossenen Kurse'
setting.lite_formation.requiredCourses.name: 'Obligatorische Kurse'
setting.lite_formation.requiredCourses.description: 'Obligatorische Kurse, die einer Ausschreibung oder einem Pfad zugeordnet sind'
setting.lite_formation.general.name: Allgemein
setting.lite_formation.general.description: Allgemein
setting.lite_formation.openedCourses.name: 'Offene Kurse'
setting.lite_formation.openedCourses.description: 'Freiwillige Kurse'
setting.lite_formation.educativeStatus.name: Bildungsniveau
setting.lite_formation.educativeStatus.description: 'Ausbildungsstand nach Punktestufen'
setting.lite_formation.gamifiedPills.name: 'Gamifizierte Pillen'
setting.lite_formation.gamifiedPills.description: 'Anzahl der spielerischen Kapitel, Misserfolge und Erfolge in spielerischen Quizzen'
setting.lite_formation.gamifiedTest.name: Test-Pillen
setting.lite_formation.gamifiedTest.description: 'Verwendete spielerische Tests sowie Treffer und Fehlversuche nach Testtyp'
setting.lite_formation.peoplePerformance.name: 'Die Leistung der Menschen'
setting.lite_formation.peoplePerformance.description: 'Leistung der Menschen'
setting.lite_formation.coursesByStars.name: 'Kurse nach Punkten'
setting.lite_formation.coursesByStars.description: 'Sternebewertung der Kurse'
setting.lite_formation.structureAndHotel.name: 'Wohnungen und Hotels'
setting.lite_formation.structureAndHotel.description: 'Prozentsatz pro Gruppe'
setting.lite_formation.schoolFinishedAndProgress.name: 'Schule Abgeschlossen und in Arbeit'
setting.lite_formation.schoolFinishedAndProgress.description: 'Schule mit den meisten Teilnahmen, Kursen in Bearbeitung und abgeschlossen'
setting.lite_formation.coursesBySchool.name: 'Kurse nach Schule'
setting.lite_formation.coursesBySchool.description: 'Anzahl der Kurse pro Kategorie'
setting.lite_formation.coursesByDepartment.name: 'Kurse nach Abteilung'
setting.lite_formation.coursesByDepartment.description: 'Erstellung von Kursen nach Abteilungen'
setting.lite_formation.usersMoreActivesByCourses.name: 'Die meisten aktiven Nutzer nach Kurs'
setting.lite_formation.usersMoreActivesByCourses.description: 'Aktivste und am wenigsten aktive Personen in abgeschlossenen Kursen'
setting.lite_evolution.name: 'Gruppe Allgemeine Statistikentwicklung'
setting.lite_evolution.description: 'Gruppe Allgemeine Statistikentwicklung'
setting.lite_evolution.trainedPerson.name: 'Ausgebildete Personen'
setting.lite_evolution.trainedPerson.description: 'Personen, die mindestens einen Kurs abgeschlossen haben'
setting.lite_evolution.startedCourses.name: 'Begonnene Kurse'
setting.lite_evolution.startedCourses.description: 'Begonnene Kurse'
setting.lite_evolution.proccessCourses.name: 'Laufende Kurse'
setting.lite_evolution.proccessCourses.description: 'Laufende Kurse'
setting.lite_evolution.finishedCourses.name: 'Abgeschlossene Kurse'
setting.lite_evolution.finishedCourses.description: 'Abgeschlossene Kurse'
setting.lite_evolution.segmentedHours.name: 'Segmentierung der Stunden'
setting.lite_evolution.userNewInPlatformThanFinishedOneCourse.name: 'Neue Nutzer, die einen Kurs abgeschlossen haben'
setting.lite_evolution.userNewInPlatformThanFinishedOneCourse.description: 'Personen, die neu auf der Plattform sind und mindestens einen Kurs absolviert haben'
setting.lite_demography.name: 'Gruppe Demografie in der allgemeinen Statistik'
setting.lite_demography.description: 'Gruppe Demografie in der allgemeinen Statistik'
setting.lite_demography.usersBySexAndAge.name: 'Nutzer nach Geschlecht und Alter'
setting.lite_demography.usersBySexAndAge.description: 'Nutzer nach Geschlecht und Alter'
setting.lite_demography.ageDistribution.name: Altersverteilung
setting.lite_demography.ageDistribution.description: Altersverteilung
setting.lite_demography.deviceDistribution.name: 'Verteilung nach Geräten'
setting.lite_demography.deviceDistribution.description: 'Verteilung nach Geräten'
setting.lite_demography.usersByCountries.name: 'Verteilung nach Ländern'
setting.lite_demography.usersByCountries.description: 'Verteilung nach Ländern'
setting.lite_activity.name: 'Allgemeine Statistik Tätigkeitsgruppe'
setting.lite_activity.description: 'Allgemeine Statistik Tätigkeitsgruppe'
setting.lite_activity.activityInfo.name: 'Informationen zur Tätigkeit'
setting.lite_activity.activityInfo.description: 'Personen, die im Portal aktiv sind, registrierte Personen, Personen, die sich in den letzten 30 Tagen mindestens einmal angemeldet haben, deaktivierte Personen und Personen, die sich nie angemeldet haben'
setting.lite_activity.accessDays.name: 'Zugang nach Tagen'
setting.lite_activity.accessDays.description: Zugangstage
setting.lite_activity.platformAccessByHours.name: 'Zugang nach Plattform und Zeit'
setting.lite_activity.platformAccessByHours.description: 'Bahnsteigzugangszeiten nach Tag und Uhrzeit (Heatmap)'
setting.lite_activity.courseStartTime.name: 'Verteilung nach Startzeiten der Kurse'
setting.lite_activity.courseStartTime.description: 'Startzeiten der Kurse'
setting.lite_activity.courseEndTime.name: 'Verteilung nach Stunden des Kursabschlusses'
setting.lite_activity.courseEndTime.description: 'Stunden der Kursabsolvierung (Heatmap)'
setting.lite_activity.coursesStartedVsFinished.name: 'Angefangene Kurse versus abgeschlossene Kurse'
setting.lite_activity.coursesStartedVsFinished.description: 'Angefangene Kurse vs. abgeschlossene Kurse'
setting.lite_activity.usersMoreActivesByActivity.name: 'Die meisten aktiven Nutzer'
setting.lite_activity.usersMoreActivesByActivity.description: 'Die aktivsten und am wenigsten aktiven Personen und ihre auf der Plattform verbrachte Zeit'
setting.lite_itinerary.name: 'Reiserouten in der Gruppe Allgemeine Statistik'
setting.lite_itinerary.description: 'Reiserouten in der Gruppe Allgemeine Statistik'
setting.lite_itinerary.itinerariesStartedAndFinished.name: 'Initiierung und Durchführung von Reiserouten'
setting.lite_itinerary.itinerariesStartedAndFinished.description: 'Begonnene und abgeschlossene Reisen'
setting.lite_itinerary.itinerariesCompletedByCountries.name: 'Abgeschlossene Routen nach Land'
setting.lite_itinerary.itinerariesCompletedByCountries.description: 'Abgeschlossene Routen nach Land'
setting.survey.hide_empty_comment.name: 'Meinung mit leerem Kommentar ausblenden'
setting.survey.hide_empty_comment.description: 'Umfragen, Namen ausblenden, wenn der Kommentar leer ist'
setting.survey.show_only_ratings.name: 'Umfrage, nur Name der Qualifikationen anzeigen'
setting.survey.show_only_ratings.description: 'Umfrage zeigt nur den Namen der Qualifikationen'
app.survey.post_nps.enabled.name: 'Name Umfrage Veröffentlichung nps aktiviert'
app.survey.post_nps.enabled.description: 'Name der Umfrage Veröffentlichung der Anwendung nps aktiviert'
setting.lite_evolution.segmentedHours.description: Stunden
setting.course.tab.person.description: 'Menschen, Kursdetails'
setting.course.showDeactivatedCourses.name: 'Registerkarte mit Namen der deaktivierten Kurse'
setting.course.showDeactivatedCourses.description: 'Zeigt die Namen der deaktivierten Kurse an'
catalog.19.name: 'Übersetzungen Administrator'
catalog.19.description: 'Übersetzungen für Administratoren'
setting.lenguage.platform: Administratorübersetzungen
setting.module.announcement.name: 'Aufruf zur Einreichung von Bewerbungen'
setting.module.announcement.description: 'Aktivieren Sie das Modul "Anrufe" im Untermenü "Kurse"'
course.diploma.index: Inhaltsübersicht
setting.zip.day_available_until.name: 'Verfügbare Tage'
setting.zip.day_available_until.description: 'Anzahl der Tage, die zur Verfügung stehen, bevor das Zip automatisch gelöscht wird.'
catalog.20.name: 'Zusätzliche Felder aufrufen'
course.diploma.filters: 'Aktivieren Sie zusätzliche Filter im Diplombericht'
setting.lenguage.platform.description: 'Verfügbare Sprachen in der Administrationsoberfläche'
translations_admin.title1: 'Zugewiesene Ausbildung'
translations_admin.title2: 'Zusätzliche Ausbildung'
translations_admin.title3: 'Zugewiesene Kurse'
translations_admin.title4: 'Freiwillige Kurse'
setting.course.tab.stats.description: 'Mit "Aktiviert" wird der Abschnitt "Statistik" auf der Detailebene eines Kurses aktiviert.'
setting.course.tab.options.description: 'Mit "Aktiviert" wird der Abschnitt "Feedback" auf der Ebene der Kursdetails aktiviert.'
course.diploma.index.description: 'Wenn diese Option aktiviert ist, wird der Abschnitt "Diplome" beim Erstellen/Ändern eines Kurses aktiviert'
setting.use.filter_in_ranking.name: 'Filter in der Nutzerbewertung verwenden'
setting.use.filter_in_ranking.description: 'Ermöglicht es Ihnen, aus dem Menü die Filter der Kategorien auszuwählen, mit denen ein Nutzer abgeglichen werden soll. Wenn diese Option deaktiviert ist, wird der Nutzer standardmäßig mit allen auf der Plattform verfügbaren Filtern verglichen'
setting.use.include_only_first_category_name: 'Nur die erste Kategorie des Filters in der Rangliste anzeigen'
setting.use.include_only_first_category_description: 'Wenn aktiv, wird nur die erste Verknüpfung des Benutzers angezeigt. Andernfalls werden alle zugehörigen Kategorien angezeigt. Wenn die Kategorie zum Beispiel "Land" ist, könnte der Benutzer sowohl mit Spanien als auch mit Nicaragua verknüpft sein.'
setting.email_support_error.name: 'Support-Mail für Fehler'
setting.email_support_error.description: 'Mails, an die die Plattformvorfälle gesendet werden sollen'
setting.export.task.slot_quantity.name: 'Anzahl der Aufgaben-Slots pro Benutzer'
setting.export.task.slot_quantity.description: 'Anzahl der für die Bearbeitung von Exportaufgaben verfügbaren Zeitnischen pro Benutzer.'
setting.export.task.long_running_type_tasks.name: 'Arten von Langzeitaufgaben'
setting.export.task.long_running_type_tasks.description: 'Liste der Aufgabentypen, die für die Ausfuhr als von langer Dauer gelten.'
setting.export.zip_task.slot_quantity.name: 'Anzahl der Zip-Task-Slots pro Benutzer'
setting.export.zip_task.slot_quantity.description: 'Anzahl der für die Verarbeitung von Zip-Komprimierungsaufgaben verfügbaren Slots pro Benutzer.'
setting.export.zip_task.long_running_type_tasks.name: 'Arten von Langzeit-Zip-Aufgaben'
setting.export.zip_task.long_running_type_tasks.description: 'Liste der Arten von Reißverschlussaufgaben, die als langwierig gelten.'
setting.export.task.user_pending_max_count_task.name: 'Maximale Anzahl anstehender Aufgaben pro Benutzer'
setting.export.task.user_pending_max_count_task.description: 'Maximale Anzahl anstehender Aufgaben, die ein Benutzer in der Warteschlange haben kann.'
setting.export.task.timeout.name: 'Zeitliche Begrenzung für Aufgaben'
setting.export.task.timeout.description: 'Maximale Zeit in Sekunden, bevor eine Exportaufgabe als abgelaufen gilt.'
setting.export.zip_task.timeout.name: 'Zeitlimit für Zip-Aufgaben'
setting.export.zip_task.timeout.description: 'Maximale Zeit in Sekunden, bevor eine Zip-Komprimierungsaufgabe als abgelaufen gilt.'
setting.export.task.timeout_seconds.name: 'Timeout-Zeit für Aufgaben im TIMEOUT-Zustand'
setting.export.task.timeout_seconds.description: 'Maximale Zeit in Sekunden, nach der eine Aufgabe im TIMEOUT-Status als nicht mehr laufend betrachtet wird.'
type_diploma.novomatic.name: Novomatic
type_diploma.novomatic.description: 'Es ist das personalisierte Diplom für Novomatic'
app.announcement.managers.sharing.name: 'Ermöglicht die Erstellung von Aufgaben in einer Aufforderung zur Einreichung von Vorschlägen'
app.announcement.managers.sharing.description: 'Ermöglicht die Erstellung von Aufgaben in einer Aufforderung zur Einreichung von Vorschlägen'
