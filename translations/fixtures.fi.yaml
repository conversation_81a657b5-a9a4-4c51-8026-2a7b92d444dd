nps_question.text.description: '<PERSON><PERSON> meille mielipiteesi'
nps_question.nps.description: '<PERSON><PERSON><PERSON> arviointi'
type_course.teleformacion.name: Verkko-oppiminen
type_course.teleformacion.description: Verkko-opiskelukurssit
type_course.presencial.name: He<PERSON><PERSON><PERSON><PERSON>htaisesti
type_course.presencial.description: 'Paikan päällä järjestettävät kurssit'
type_course.mixto.name: Mixed
type_course.mixto.description: 'Se on yhdistelmä verkko-opiskelua ja henkilökohtaista koulutusta'
type_course.aula_virtual.name: 'Virtuaalinen luokkahuone'
type_course.aula_virtual.description: 'Kurs<PERSON>t järjestetään videokonferenssin välityksellä'
alert_type_tutor.1.name: 'Soittaja ei ole osallistunut kurssille'
alert_type_tutor.1.description: 'Tutorille lähetetään hälyty<PERSON>, jos kutsuttu henkilö ei ole päässyt kurssille'
alert_type_tutor.2.name: '50 prosenttia puhelun kestosta on kulunut ja 25 prosenttia sisällöstä on vielä kesken'
alert_type_tutor.2.description: 'Opettajalle lähetetään hälytys, jos 50 % puhelun kestosta on kulunut ja 25 % sisällöstä on kesken'
alert_type_tutor.3.name: 'Puhelusta on kulunut 80 prosenttia ja 50 prosenttia sisällöstä on vielä kesken'
alert_type_tutor.3.description: 'Opettajalle lähetetään hälytys, jos 80 prosenttia puhelun kestosta on kulunut ja 50 prosenttia sisällöstä on vielä kesken'
alert_type_tutor.4.name: 'Puhelun päättymiseen on enää muutama päivä aikaa, eikä kurssia ole vielä saatu päätökseen'
alert_type_tutor.4.description: 'Sinun on arvioitava, kuinka monta päivää pidetään vähinä päivinä'
alert_type_tutor.5.name: 'Soitettu henkilö on suorittanut kurssin, mutta ei ole vastannut kyselyyn.'
alert_type_tutor.5.description: 'Jos alustalla on kyselyt, ohjaajalle lähetetään hälytys, jos kutsuttu henkilö on suorittanut kurssin loppuun, mutta ei ole vastannut kyselyyn'
alert_type_tutor.6.name: 'Henkilö on suorittanut kurssin, mutta ei ole ladannut tutkintotodistusta'
alert_type_tutor.6.description: 'Tutorille lähetetään hälytys, jos kutsuttu henkilö on suorittanut kurssin, mutta ei ole ladannut tutkintotodistusta.'
announcement_configuration_type.temporalizacion.name: Ajoitus
announcement_configuration_type.temporalizacion.description: 'Kurssin seurannan helpottamiseksi annamme kullekin sisältö- ja toimintalohkolle ajan, jolloin pystymme havaitsemaan, ketkä osallistujista työskentelevät riittävällä tahdilla tai ovat jääneet jälkeen koulutusprosessissa'
announcement_configuration_type.curso_bonificado.name: 'Tuettu kurssi'
announcement_configuration_type.curso_bonificado.description: 'Tuetut kurssit ovat Tripartite-säätiön kautta toteutettavia kursseja, jotka yritykset rahoittavat sosiaaliturvamaksuilla.'
announcement_configuration_type.chat.name: Chat
announcement_configuration_type.chat.description: 'Chat on synkroninen viestintäväline, jonka avulla kurssin osallistujat voivat olla vuorovaikutuksessa reaaliajassa tekstiviestien välityksellä.'
announcement_configuration_type.notificaciones.name: Ilmoitukset
announcement_configuration_type.notificaciones.description: 'Ilmoitukset ovat viestejä, jotka lähetetään kurssilaisille ja joissa kerrotaan tärkeistä uutisista tai tapahtumista.'
announcement_configuration_type.mensajeria.name: Viestinvälitys
announcement_configuration_type.mensajeria.description: 'Viestijärjestelmä on viestintäjärjestelmä, jonka avulla kurssilaiset voivat lähettää ja vastaanottaa yksityisiä viestejä.'
announcement_configuration_type.foros.name: Foorumit
announcement_configuration_type.foros.description: 'Foorumi on asynkroninen viestintätila, jossa kurssilaiset voivat vaihtaa viestejä tietystä aiheesta.'
announcement_configuration_type.diploma.name: Tutkintotodistus
announcement_configuration_type.diploma.description: 'Tutkintotodistukset ovat todistuksia, jotka annetaan kurssin osallistujille kurssin suorittamisesta.'
announcement_configuration_type.tutor_alerts.name: 'Aktivoi opettajan hälytykset'
announcement_configuration_type.tutor_alerts.description: 'Hälytykset ovat viestejä, jotka lähetetään kurssin ohjaajalle ja joissa kerrotaan tärkeistä uutisista tai tapahtumista.'
announcement_configuration_type.encuesta_satisfaccion.name: Tyytyväisyyskysely
announcement_configuration_type.encuesta_satisfaccion.description: 'Tyytyväisyyskyselyt ovat kurssilaisille lähetettyjä kyselyitä, joilla selvitetään heidän mielipiteitään kurssista.'
announcement_configuration_type.finalizar_convocatoria.name: 'Kurssi pysyy aktiivisena puhelun päätyttyä'
announcement_configuration_type.finalizar_convocatoria.description: 'Käyttäjä pääsee kurssin sisältöön kurssin päätyttyä.'
announcement_configuration_type.firma_digital.name: 'Digitaalinen allekirjoitus'
announcement_configuration_type.firma_digital.description: 'Digitaalinen allekirjoitus vaaditaan, jotta voit allekirjoittaa läsnäolon kasvokkain suoritettavalle kurssille.'
announcement_configuration_type.gestion_costes.name: 'Kustannusten hallinta'
announcement_configuration_type.gestion_costes.description: 'Kustannusten hallinnan avulla ryhmät voivat ilmoittaa puhelun kustannukset.'
announcement_configuration_type.EMAIL_NOTIFICATION_ON_ANNOUNCEMENT.name: 'EMAIL_NOTIFICATION_ON_ANNOUNCEMENT (SÄHKÖPOSTI-ILMOITUS_ILMOITUKSESTA)'
announcement_configuration_type.EMAIL_NOTIFICATION_ON_ANNOUNCEMENT.description: 'Ota sähköposti-ilmoitukset käyttöön.'
announcement_configuration_type.NOTIFICATION_ON_ANNOUNCEMENT.name: NOTIFICATION_ON_ANNOUNCEMENT
announcement_configuration_type.NOTIFICATION_ON_ANNOUNCEMENT.description: 'Ota normaalit ilmoitukset käyttöön'
announcement_configuration_type.objetivos_contenidos.name: 'Tavoitteet ja sisältö'
announcement_configuration_type.objetivos_contenidos.description: 'Kurssin tavoitteet ja sisältö sisällytetään tutkintotodistukseen.'
announcement_configuration_type.dni.name: Dni
announcement_configuration_type.dni.description: 'Opiskelijan henkilötunnus merkitään todistukseen'
announcement_configuration_type.template_excel.name: 'Rekisteröintimalli excel'
announcement_configuration_type.template_excel.description: 'Tätä mallia käytetään opiskelijoiden ilmoittautumisessa käyttämällä HRBP-koodia DNI-tunnuksen sijasta.'
announcement_configuration_type.report_zip.name: ZIP
announcement_configuration_type.report_zip.description: 'Anna ohjaajan ladata ryhmäraportit ZIP-muodossa'
announcement_criteria.1.name: 'Täydelliset luvut'
announcement_criteria.1.description: 'Hakukriteerin täyttämiseen vaadittavien lukujen prosenttiosuus.'
announcement_criteria.2.name: 'Tehtävien suorittaminen'
announcement_criteria.2.description: '% tehtävistä, joiden on edistettävä ehdotuspyynnön hyväksymiskriteerin täyttymistä.'
announcement_criteria.3.name: 'Suurin mahdollinen seisokkiaika'
announcement_criteria.3.description: 'Kun tämä määrä minuutteja ruutuaikaa ilman passiivisuutta on saavutettu, ajan laskenta keskeytetään, kunnes käyttäjän toimintaa havaitaan uudelleen.'
announcement_criteria.4.name: 'Toiminnan loppuun saattaminen'
announcement_criteria.4.description: 'Kun tämä määrä minuutteja ruutuaikaa ilman passiivisuutta on saavutettu, ajan laskenta keskeytetään, kunnes käyttäjän toimintaa havaitaan uudelleen.'
announcement_criteria.5.name: 'Aika suoritettu'
announcement_criteria.5.description: 'Puhelun läpäisykriteerin täyttämiseen vaadittavien tuntien prosenttiosuus.'
announcement_step_creation.ANNOUNCEMENT_COURSE.description: 'Ensimmäinen vaihe puhelun luomisessa'
announcement_step_creation.ANNOUNCEMENT_GENERAL_INFO.description: 'Puhelun luomisen toinen vaihe, jossa täytetään yleiset tiedot'
announcement_step_creation.ANNOUNCEMENT_BONUS.description: 'Tämä vaihe riippuu siitä, onko asiakas aktivoinut bonuksen'
announcement_step_creation.ANNOUNCEMENT_STUDENTS.description: 'Tässä vaiheessa puheluun lisätään opiskelijoita, jotka voidaan määrittää ryhmään'
announcement_step_creation.ANNOUNCEMENT_GROUPS.description: 'Tässä vaiheessa määritetään edellisessä vaiheessa luodut opiskelijaryhmät'
announcement_step_creation.ANNOUNCEMENT_COMMUNICATION.description: 'Tässä vaiheessa määritetään opiskelijoille lähetettävä kysely'
announcement_step_creation.ANNOUNCEMENT_SURVEY.description: 'Tässä vaiheessa määritetään opiskelijoille lähetettävä kysely'
announcement_step_creation.ANNOUNCEMENT_CERTIFICATE.description: 'Tässä vaiheessa näytetään alustalla saatavilla olevat tutkintotodistukset, joista asiakas voi valita haluamansa'
announcement_step_creation.ANNOUNCEMENT_ALERTS.description: 'Nämä hälytykset ovat erityisiä hälytyksiä, joilla ilmoitetaan ohjaajalle puhelun tapahtumista'
class_room_virtual.zoom.name: Zoomaus
class_room_virtual.zoom.description: 'Online-verkkoneuvottelualusta, joka mahdollistaa teräväpiirtovideopuhelut, työpöydän jakamisen, valkotaulun, chatin, konferenssin tallentamisen, asiakirjojen jakamisen ja pääsyn mistä tahansa, koska se on saatavilla mobiililaitteille.'
class_room_virtual.clickmeeting.name: ClickMeeting
class_room_virtual.clickmeeting.description: 'ClickMeetingin alusta on yksi markkinoiden käyttäjäystävällisimmistä webinaari-käyttöliittymistä, ja se tarjoaa runsaasti joustavia räätälöintimahdollisuuksia'
class_room_virtual.jitsi.name: Jitsi
class_room_virtual.jitsi.description: 'Avoimen lähdekoodin ratkaisu videoneuvotteluihin, joissa on salatut yhteydet ja jotka ovat saatavilla eri käyttöjärjestelmille'
class_room_virtual.plugnmeet.name: PlugnMeet
class_room_virtual.plugnmeet.description: 'Helppo integroitava ja hyvin mukautettava avoimen lähdekoodin videoneuvotteluohjelmisto'
configuration_cliente_announcement.COMMUNICATION.description: 'Tämä mahdollistaa viestinnän käyttöönoton puhelun aikana'
configuration_cliente_announcement.CERTIFICATE.description: 'Tämä mahdollistaa tutkintotodistusten lataamisen puhelun aikana'
configuration_cliente_announcement.SURVEY.description: 'Näin kyselyt voidaan ottaa käyttöön, jotta ne ovat käytettävissä myöhemmin puhelun aikana'
configuration_cliente_announcement.ALERT.description: 'Tämä mahdollistaa hälytysten ottamisen käyttöön, jotka periytyvät tutorin hälytysosioon'
configuration_cliente_announcement.TEMPORALIZATION.description: 'Mahdollistaa ehdotuspyynnön lukujen ajallisen sijoittelun'
configuration_cliente_announcement.BONIFICATION.description: 'Bonus kutsusta, erityisesti kolmikantasäätiön kutsusta'
configuration_cliente_announcement.ACCESS_CONTENT.description: 'Mahdollistaa pääsyn puhelun sisältöön, kun puhelu on päättynyt'
configuration_cliente_announcement.DIGITAL_SIGNATURE.description: 'Mahdollistaa digitaalisen allekirjoituksen käyttöönoton hakuilmoituksessa, erityisesti kasvokkain järjestettävillä kursseilla'
configuration_cliente_announcement.COST.description: 'Mahdollistaa asiakkaille kustannusten kohdentamisen puheluun.'
configuration_cliente_announcement.NOTIFICATION_ACTIVATE_ANNOUNCEMENT.description: 'Ilmoitukset puhelun aktivoinnista (sähköposti, etukäteisilmoitus)'
configuration_cliente_announcement.CONFIGURATION_IBEROSTAR.description: 'Tämä kokoonpano on suunniteltu erityisesti Iberostar-asiakkaille, jotta se ei vaikuttaisi rahavirtoihin'
configuration_cliente_announcement.REPORT.description: 'Raportoinnin mahdollistaminen ehdotuspyynnöissä'
type_course_announcement_step_creation.seleccionar_curso.name: 'Valitse kurssi'
type_course_announcement_step_creation.seleccionar_curso.description: 'Valitse kurssi, jota varten puhelu luodaan'
type_course_announcement_step_creation.convocatoria.name: Hakuilmoitus
type_course_announcement_step_creation.convocatoria.description: 'Ehdotuspyyntöä koskevat tiedot'
type_course_announcement_step_creation.bonificacion.name: Bonus
type_course_announcement_step_creation.bonificacion.description: 'Ehdotuspyyntöä koskevat tiedot'
type_course_announcement_step_creation.alumnado.name: Alumni
type_course_announcement_step_creation.alumnado.description: 'Opiskelijat lisätään kurssille'
type_course_announcement_step_creation.grupos.name: Ryhmät
type_course_announcement_step_creation.grupos.description: 'Ryhmän tiedot ovat yksityiskohtaiset ja myös ohjaaja on lisätty'
type_course_announcement_step_creation.comunicacion.name: Viestintä
type_course_announcement_step_creation.comunicacion.description: 'Tämä vaihe riippuu siitä, onko asiakas aktivoinut viestinnän vai ei.'
type_course_announcement_step_creation.encuesta.name: Tutkimus
type_course_announcement_step_creation.encuesta.description: 'Tämä vaihe riippuu siitä, onko asiakas aktivoinut kyselyn vai ei.'
type_course_announcement_step_creation.diploma.name: Tutkintotodistus
type_course_announcement_step_creation.diploma.description: 'Tämä vaihe riippuu siitä, onko asiakkaalla aktivoitu tutkintotodistus vai ei.'
type_course_announcement_step_creation.alertas.name: Hälytykset
type_course_announcement_step_creation.alertas.description: 'Tämä voi riippua asiakkaan kokoonpanosta.'
type_diploma.easylearning.name: Oletusarvo
type_diploma.easylearning.description: 'Se on asiakasyrityksen tutkintotodistus'
type_diploma.fundae.name: Fundae
type_diploma.fundae.description: 'Se on Fundae-diplomi'
type_diploma.hobetuz.name: Hobetuz
type_diploma.hobetuz.description: 'Se on Hobetuzin tutkintotodistus'
type_money.euro.name: Euro
type_money.euro.country: Espanja
type_money.dolar_estadounidense.name: 'Yhdysvaltain dollari'
type_money.dolar_estadounidense.country: Yhdysvallat
section_default_front.mi_formacion.name: Koulutukseni
section_default_front.mi_formacion.description: 'Määritetyn koulutuksen sisältä löydät kaikki sinulle osoitetut kurssit.'
section_default_front.formacion_adicional.name: Lisäkoulutus
section_default_front.formacion_adicional.description: 'Tästä osiosta löydät kaikki avoimen kampuksen kurssit.'
section_default_front.formacion_asignada.name: 'Määrätty koulutus'
section_default_front.formacion_asignada.description: 'Tästä osiosta löydät kaikki sinulle osoitetut kurssit.'
setting.multi_idioma.name: Monikielinen
setting.multi_idioma.description: 'Se tarjoaa monikielisen käyttöliittymän'
setting.default_lenguage.name: Oletuskieli
setting.default_lenguage.description: 'Järjestelmän käyttöliittymän oletuskieli'
setting.languages.name: Kieli
setting.languages.description: 'Sovelluksessa käytettävissä olevat kielet'
setting.registro_libre.name: 'Ilmainen käyttäjän rekisteröinti'
setting.registro_libre.description: 'Tila, jossa käyttäjä voi rekisteröityä vapaasti alustalle täytettyään vastaavan lomakkeen.'
setting.opinion_plataforma.name: 'Alustan mielipiteet'
setting.opinion_plataforma.description: 'Alustan mielipiteet'
setting.validacion_automatica.name: 'Automaattinen käyttäjän rekisteröinnin validointi'
setting.validacion_automatica.description: 'Automaattinen käyttäjän rekisteröinnin validointi'
setting.filtros_plataforma.name: 'Alustan suodattimet'
setting.filtros_plataforma.description: 'Tämän avulla voit aktivoida tai deaktivoida alustan suodattimet'
setting.itinearios_plataforma.name: 'Matkareitit alustalla'
setting.itinearios_plataforma.description: 'Sen tarkoituksena on aktivoida tai deaktivoida reittejä alustalla'
setting.seccion_cursos.name: 'Kurssin osat [FRONT]'
setting.seccion_cursos.description: 'Kurssiosuudet [FRONT],'
setting.set_points_course.name: 'Aseta kurssin pisteet'
setting.set_points_course.description: 'Tätä käytetään pisteiden antamiseen kursseille, kun niitä luodaan tai muokataan, erityisesti verkko-opiskelukurssia varten'
setting.default_points_course.name: 'Kurssin oletuspisteet'
setting.default_points_course.description: 'Jos käytetään pelikaavoja, jos luku ei ole peli, annetaan puoli pistettä'
setting.documentation_course.name: 'Yleisiä tietoja'
setting.documentation_course.description: 'Aktivoi froala lisätäksesi yleiset kurssitiedot kurssin vaiheessa 2'
setting.open_course.name: 'Avoimet kurssit'
setting.open_course.description: 'Näin alustaan voidaan lisätä avoimia kursseja tai lisäkoulutusta'
setting.client_id.name: 'Vimeo asiakastunnus'
setting.client_id.description: 'Tämä on vimeo-asiakkaan tunniste'
setting.client_secret.name: 'Salainen vimeo-asiakas'
setting.client_secret.description: 'Salainen vimeo-asiakas'
setting.access_token.name: Pääsymerkki
setting.access_token.description: Pääsymerkki
setting.user_id.name: Käyttäjätunnus
setting.user_id.description: 'Vimeo rekisteröitynyt käyttäjätunnus'
setting.project_id.name: 'Videon luvun kansio'
setting.project_id.description: 'Tämä on tunniste, jossa videolukujen resurssit sijaitsevat'
setting.project_id_resource_course.name: 'Materiaalien resurssikansio (ehdotuspyyntö)'
setting.project_id_resource_course.description: 'Tämä on sen kansion tunniste, johon kurssimateriaaliin ja puheluun liittyvät videot on tallennettu'
setting.project_id_task_course.name: 'Tehtävä Resurssikansio'
setting.project_id_task_course.description: 'Se on sen kansion tunniste, johon kurssin tehtäviin ja kutsuun liittyvät videot on tallennettu'
setting.project_id_video_Quiz.name: Videoquiz-resurssikansio
setting.project_id_video_Quiz.description: 'Se on sen kansion tunniste, jossa videokilpailupeliin liittyvät videot sijaitsevat'
setting.project_id_Roleplay.name: 'Roolileikin resurssipakkaus'
setting.project_id_Roleplay.description: 'Videotyyppisten resurssien tunnistaminen roolipelissä'
setting.upload_sudomain.name: 'Lataa alatunnukseen'
setting.upload_sudomain.description: 'Tätä muuttujaa käytetään videoiden ja SCORM-tiedostojen lataamiseen, jolloin Cloudflare-rajoitukset voidaan ylittää'
setting.from_email.name: Sähköpostista
setting.from_email.description: 'Se on alustalta lähetettyjen sähköpostiviestien alkuperä'
setting.from_name.name: 'Alustan nimestä'
setting.from_name.description: 'Sähköposteissa ja tutkintotodistuksissa näkyvä foorumin nimi'
setting.from_cif.name: 'CIF:stä'
setting.from_cif.description: 'Yrityksen alv-numero'
setting.email_support.name: Sähköpostituki
setting.email_support.description: 'Näitä sähköposteja käytetään tuki-ilmoitusten lähettämiseen'
setting.email_support_register.name: 'Sähköpostivastaavan vastaanotto'
setting.email_support_register.description: 'Tätä sähköpostia käytetään rekisteröintipyyntöjen vastaanottamiseen alustalla'
setting.news.name: Uutiset
setting.news.description: Uutiset
setting.foro.name: Foorumi
setting.foro.description: Foorumi
setting.desafios.name: Haasteet
setting.desafios.description: Haasteet
setting.secciones.name: Jaksot
setting.secciones.description: 'Tämän muuttujan avulla voit määrittää, näytetäänkö osiot etusivulla'
setting.encuestas.name: Kyselyt
setting.encuestas.description: Kyselyt
setting.active_cron_exports.name: 'Aktiivinen cron vienti'
setting.active_cron_exports.description: 'Aktiivinen cron-vienti, jota käytetään yleensä tietojen vientiin alustalta'
setting.gender_excel.name: 'Genero excel'
setting.gender_excel.description: 'Käytetään sukupuolen lisäämiseksi viennissä'
setting.code.name: Koodi
setting.code.description: 'Näytä koodikenttä kurssitilastojen viennissä'
setting.finished_chapters.name: 'Valmiit luvut'
setting.finished_chapters.description: 'näytä valmiit luvut kurssitilasto-vientitiedostossa (course-stats-export)'
setting.zoom_cliente_id.name: 'Zoom asiakkaan ID'
setting.zoom_cliente_id.description: 'Zoom-asiakastunnus - tarvitaan Zoom API:n käyttämiseen'
setting.zoom_cliente_secret.name: 'Zoom-asiakkaan salaisuus'
setting.zoom_cliente_secret.description: 'zoom-asiakasavain - tarvitaan zoomaus-API:n käyttämiseen'
setting.zoom_account_id.name: 'Zoom-tilin tunnus'
setting.zoom_account_id.description: 'Zoom-asiakastilin numero - tarvitaan Zoom API:n käyttämiseen'
setting.zoom_email.name: 'Zoom Email'
setting.zoom_email.description: 'zoom-asiakkaan sähköposti - tarvitaan Zoom API:n käyttämiseen'
setting.clickmeeting_api_key.name: 'ClickMeeting API-avain'
setting.clickmeeting_api_key.description: 'ClickMeeting-asiakastunnus - tarvitaan ClickMeeting API:n käyttämiseen'
setting.clikmeeting_dirbase.name: 'ClickMeetingin perushakemisto'
setting.clikmeeting_dirbase.description: 'ClickMeeting-palvelimen osoite'
setting.clikmeeting_events_paralel.name: 'ClickMeetingin sivutapahtumat'
setting.clikmeeting_events_paralel.description: 'Sopimuksen mukaisten sivutapahtumien määrä'
setting.plugnmeet_serverurl.name: 'PlugNmeet-palvelimen Url'
setting.plugnmeet_serverurl.description: 'PlugNmeet-palvelimen osoite'
setting.plugnmeet_api_key.name: 'PlugNmeet API-avain'
setting.plugnmeet_api_key.description: 'PlugNmeet-asiakkaan ID'
setting.plugnmeet_secret.name: 'PlugNmeetin salainen avain'
setting.plugnmeet_secret.description: PlugNmeet-asiakasavain
setting.plugnmeet_analyticsurl.name: 'PlugNmeet URL-analytiikka'
setting.plugnmeet_analyticsurl.description: 'PlugNmeet-palvelimen osoite analytiikkaa varten'
setting.zoom_urlreports.name: 'Suurenna raportti Url'
setting.zoom_urlreports.description: 'Osoite, johon zoomausraportit tallennetaan'
setting.plugnmeet_urlreports.name: 'PlugNmeet raportointi url'
setting.plugnmeet_urlreports.description: 'Osoite, johon plugNmeet-raportit tallennetaan'
setting.clickmeeting_urlreports.name: 'ClickMeeting-raportoinnin URL-osoite'
setting.clickmeeting_urlreports.description: 'Osoite, johon ClickMeeting-raportit tallennetaan'
setting.library_enabled.name: 'Kirjasto käytössä'
setting.library_enabled.description: 'Kirjasto käytössä'
setting.library_audio_local.name: 'Kirjaston paikallinen ääni'
setting.library_audio_local.description: 'Kirjaston paikallinen ääni'
setting.library_audio_path.name: 'Kirjaston äänipolku'
setting.library_audio_path.description: 'Kirjaston äänipolku'
setting.library_file_path.name: 'Kirjaston tiedostopolku'
setting.library_file_path.description: 'Kirjaston tiedostopolku'
setting.library_data_page_size.name: 'Kirjastotietojen sivukoko'
setting.library_data_page_size.description: 'Kirjastotietojen sivukoko'
setting.library_comments.name: 'Kirjaston kommentit'
setting.library_comments.description: 'Kirjaston kommentit'
setting.challenge_loops.name: Looping-haasteet
setting.challenge_loops.description: Looping-haasteet
setting.points_for_win.name: 'Pisteet voitosta'
setting.points_for_win.description: 'Pisteet voitosta'
setting.points_for_lose.name: 'Pisteet häviämisestä'
setting.points_for_lose.description: 'Pisteet häviämisestä'
setting.points_fortie.name: 'Pisteet tasapelistä'
setting.points_fortie.description: 'Pisteet tasapelistä'
setting.points_corrects.name: 'Pisteet oikeasta'
setting.points_corrects.description: 'Pisteet oikeasta'
setting.points_for_left.name: 'Pisteet jäljellä'
setting.points_for_left.description: 'Pisteet jäljellä'
setting.total_duels.name: 'Kaksintaistelujen kokonaismäärä'
setting.total_duels.description: 'Kaksintaistelujen kokonaismäärä'
setting.seconds_per_question.name: 'Sekuntia kysymystä kohti'
setting.seconds_per_question.description: 'Sekuntia kysymystä kohti'
setting.user_dni.name: Käyttäjätunnus
setting.user_dni.description: 'Tämä tulee näkyviin, kun luodaan tai muokataan käyttäjää'
setting.edit_code.name: 'Koodin muokkaaminen'
setting.edit_code.description: 'Se on käyttäjän yksilöllinen tunniste'
setting.stats_acumulative.name: 'Kumulatiiviset tilastot'
setting.stats_acumulative.description: 'Tämä on tarpeen, jos haluat, että tilastot ovat kumulatiivisia'
setting.maximo_fechas.name: Enimmäispäivämääräalue
setting.maximo_fechas.description: 'Kuulemisten enimmäispäivämäärät'
setting.maximo_horas.name: 'Enimmäispyynnöt tunnissa'
setting.maximo_horas.description: 'Enimmäispyynnöt tunnissa'
setting.maximo_dia.name: 'Enimmäispyynnöt päivässä'
setting.maximo_dia.description: 'Enimmäispyynnöt päivässä'
setting.fundae.name: Fundae
setting.fundae.description: 'Jos tämä on käytössä, kun puhelu julkaistaan, käyttäjien on täytettävä kaikki tarvittavat users_extra_fundae-taulun kentät'
setting.margen_entrada.name: 'Oletusarvoinen tulomarginaali'
setting.margen_entrada.description: 'Oletussyöttömarginaali, jota käytetään QR-koodissa'
setting.margen_salida.name: Oletustulosmarginaali
setting.margen_salida.description: 'Oletusarvoinen tulostusmarginaali, jota käytetään QR-koodissa'
setting.registrar_qr.name: 'Rekisteröidy QR-istunnon avulla'
setting.registrar_qr.description: 'Jos tämä on käytössä, istunnot kirjataan QR-koodilla'
setting.maximo_alumnos.name: 'Opiskelijoiden enimmäismäärä ryhmää kohti'
setting.maximo_alumnos.description: 'Opiskelijoiden enimmäismäärä ryhmää kohti'
setting.min_score.name: Vähimmäispistemäärä
setting.min_score.description: Vähimmäispistemäärä
setting.types_action.name: Toimintatyypit
setting.types_action.description: Toimintatyypit
setting.materiales_convocatoria.name: 'Mahdollistetaan ehdotuspyyntöjen aineiston luominen'
setting.materiales_convocatoria.description: 'Mahdollistetaan ehdotuspyyntöjen aineiston luominen'
setting.tareas_convocatoria.name: 'Mahdollistaa tehtävien luomisen ehdotuspyynnössä'
setting.tareas_convocatoria.description: 'Mahdollistaa tehtävien luomisen ehdotuspyynnössä'
setting.minimo_minutos.name: 'Pienin seisokkiaika minuutteina'
setting.minimo_minutos.description: 'Vähimmäiskeskeytysaika minuutteina, koskee alustalla olevia käyttäjiä'
setting.timezones.name: 'Ehdotuspyynnössä sallitut aikavyöhykkeet'
setting.timezones.description: 'Aikavyöhyke, joka voidaan konfiguroida ehdotuspyyntöön'
catalog.1.name: 'Luvun tyypit'
catalog.1.description: 'Alustalla saatavilla olevien lukujen tyypin määrittäminen'
catalog.2.name: Kurssityypit
catalog.2.description: 'Alustalla saatavilla olevien kurssityyppien määrittäminen'
catalog.3.name: Hyväksymisperusteet
catalog.3.description: 'Hyväksymisperusteiden konfigurointi, joka on saatavilla alustalla'
catalog.4.name: Tutor-ilmoitukset
catalog.4.description: 'Opettajien hälytysten määrittäminen, jotka ovat saatavilla alustalla'
catalog.5.name: 'Tutkintotodistusten tyypit'
catalog.5.description: 'Alustalla saatavilla olevien tutkintotodistusten tyypin määrittäminen'
catalog.6.name: 'Asiakkaan kokoonpano puhelun aikana'
catalog.6.description: 'Ehdotuspyynnössä näytettävien vaiheiden määrittäminen'
catalog.7.name: Kolikkotyypit
catalog.7.description: 'Alustalla käytettävissä olevien valuuttojen tyypin määrittäminen'
catalog.8.name: 'Kokoonpanojen ryhmä'
catalog.8.description: 'Ryhmä kokoonpanoja, jotka ovat saatavilla alustalla'
catalog.9.name: Kokoonpanot
catalog.9.description: 'Ryhmäkohtaiset kokoonpanot, jotka ovat käytettävissä alustalla'
catalog.10.name: Yritys
catalog.10.description: 'Käyttäjäyritykset, jotka ovat saatavilla alustalla osoitteessa'
catalog.11.name: 'Ammatillinen luokka'
catalog.11.description: 'Ammattimaiset käyttäjäryhmät, jotka ovat käytettävissä alustalla'
catalog.12.name: 'Käyttäjän työkeskus'
catalog.12.description: 'Työpaikat käyttäjille, jotka ovat saatavilla alustalla'
catalog.13.name: 'Käyttäjätyön osasto'
catalog.13.description: 'Käyttäjille tarkoitetut osastot, jotka ovat saatavilla alustalla'
catalog.14.name: 'Käyttäjän tutkimustaso'
catalog.14.description: 'Käyttäjien opintotasot, jotka ovat saatavilla alustalla'
catalog.15.name: 'Eri kurssityyppien vaiheet'
catalog.15.description: 'Alustalla saatavilla olevien erityyppisten kurssien vaiheiden määrittäminen'
catalog.16.name: Virtuaaliluokkahuonetyypit
catalog.16.description: 'Alustalla saatavilla olevien erityyppisten kurssien virtuaaliluokkahuoneiden tyypit'
catalog.17.name: Tunnistamistyypit
catalog.17.description: 'Alustalla käytettävissä olevat tunnistetyypit'
nps_question.text.name: Teksti
nps_question.text.descripction: 'Kerro meille mielipiteesi'
setting.help.user.name: 'Sisällytä ohje pdf käyttäjän valikkoon'
setting.help.user.description: 'Tämä tuki on luotu erityisesti iberostaria varten'
catalog.18.name: 'Henkilökohtaisten puhelujen muodot'
catalog.18.description: 'Tämä on Iberostarille erityisen tärkeää'
setting.userPolicies_plataforma.name: Tietosuojakäytäntö
setting.userPolicies_plataforma.description: 'Tätä muuttujaa käytetään ottamaan käyttöön modaalinen näkymä etupäässä, kun käyttäjä ei hyväksy tietosuojakäytäntöä'
setting.course.tab.person.name: 'Välilehti ihmiset kurssin tiedot'
setting.course.tab.stats.name: "Tilastotaulukko kurssin tiedot\n"
setting.course.tab.opinions.name: "Mielipiteiden välilehti kurssin tiedot\n"
setting.documentation.name: Dokumentaatio
setting.documentation.description: 'Aktivoi sivuvalikosta Documentation-moduuli'
setting.user_company.name: Yritykset
setting.user_company.description: 'Aktivoi Yritykset-moduuli sivuvalikosta'
setting.pages.name: Alatunniste
setting.pages.description: 'Aktivoi alatunniste kampuksella'
setting.lite_formation.name: 'Yleisten tilastojen koulutusryhmä'
setting.lite_formation.description: 'Yleisten tilastojen koulutusryhmä'
setting.lite_formation.formationHours.name: Koulutustunnit
setting.lite_formation.formationHours.description: 'Koulutuksen kokonaistunnit ja keskimääräinen koulutustuntimäärä henkilöä kohden'
setting.lite_formation.peopleWithCourses.name: 'Ihmiset, joilla on kursseja'
setting.lite_formation.peopleWithCourses.description: 'Koulutuksessa olevat henkilöt ja vähintään yhden kurssin suorittaneet henkilöt'
setting.lite_formation.courseStartedAndFinished.name: 'Aloitetut, käynnissä olevat ja päättyneet kurssit'
setting.lite_formation.courseStartedAndFinished.description: 'Aloitettujen, käynnissä olevien ja suoritettujen kurssien määrä'
setting.lite_formation.requiredCourses.name: 'Pakolliset kurssit'
setting.lite_formation.requiredCourses.description: 'Pakolliset kurssit, jotka on määritetty tietylle kutsulle tai opintopolulle'
setting.lite_formation.general.name: Yleistä
setting.lite_formation.general.description: Yleistä
setting.lite_formation.openedCourses.name: 'Avoimet kurssit'
setting.lite_formation.openedCourses.description: 'Vapaaehtoiset kurssit'
setting.lite_formation.educativeStatus.name: Koulutustaso
setting.lite_formation.educativeStatus.description: 'Koulutustilanne pistetasoittain'
setting.lite_formation.gamifiedPills.name: 'Pelillistetyt pillerit'
setting.lite_formation.gamifiedPills.description: 'Pelillistettyjen lukujen määrä, epäonnistumiset ja onnistumiset pelillistetyissä tietokilpailuissa'
setting.lite_formation.gamifiedTest.name: Testipillerit
setting.lite_formation.gamifiedTest.description: 'Käytetyt pelilliset testit sekä onnistumiset ja epäonnistumiset testityypeittäin'
setting.lite_formation.peoplePerformance.name: 'Ihmisten suorituskyky'
setting.lite_formation.peoplePerformance.description: 'Ihmisten suorituskyky'
setting.lite_formation.coursesByStars.name: 'Kurssit pistemäärän mukaan'
setting.lite_formation.coursesByStars.description: 'Kurssien tähtiluokitus'
setting.lite_formation.structureAndHotel.name: 'Asunnot ja hotellit'
setting.lite_formation.structureAndHotel.description: 'Prosenttiosuus ryhmää kohti'
setting.lite_formation.schoolFinishedAndProgress.name: 'Koulu Toteutettu ja käynnissä'
setting.lite_formation.schoolFinishedAndProgress.description: 'Koulu, jossa on eniten osallistujia, meneillään olevat ja suoritetut kurssit'
setting.lite_formation.coursesBySchool.name: 'Kurssit kouluittain'
setting.lite_formation.coursesBySchool.description: 'Kurssien määrä luokittain'
setting.lite_formation.coursesByDepartment.name: 'Kurssit osastoittain'
setting.lite_formation.coursesByDepartment.description: 'Kurssien luominen osastoittain'
setting.lite_formation.usersMoreActivesByCourses.name: 'Aktiivisimmat käyttäjät kursseittain'
setting.lite_formation.usersMoreActivesByCourses.description: 'Eniten ja vähiten aktiiviset henkilöt suoritetuilla kursseilla'
setting.lite_evolution.name: 'Yleisten tilastojen kehittämisryhmä'
setting.lite_evolution.description: 'Yleisten tilastojen kehittämisryhmä'
setting.lite_evolution.trainedPerson.name: 'Koulutetut henkilöt'
setting.lite_evolution.trainedPerson.description: 'Henkilöt, jotka ovat suorittaneet vähintään yhden kurssin'
setting.lite_evolution.startedCourses.name: 'Aloitetut kurssit'
setting.lite_evolution.startedCourses.description: 'Aloitetut kurssit'
setting.lite_evolution.proccessCourses.name: 'Käynnissä olevat kurssit'
setting.lite_evolution.proccessCourses.description: 'Käynnissä olevat kurssit'
setting.lite_evolution.finishedCourses.name: 'Suoritetut kurssit'
setting.lite_evolution.finishedCourses.description: 'Suoritetut kurssit'
setting.lite_evolution.segmentedHours.name: 'Tuntien segmentointi'
setting.lite_evolution.userNewInPlatformThanFinishedOneCourse.name: 'Uudet käyttäjät, jotka ovat suorittaneet kurssin'
setting.lite_evolution.userNewInPlatformThanFinishedOneCourse.description: 'Alustan uudet käyttäjät, jotka ovat suorittaneet vähintään yhden kurssin'
setting.lite_demography.name: 'Yleisten tilastojen demografiaryhmä'
setting.lite_demography.description: 'Yleisten tilastojen demografiaryhmä'
setting.lite_demography.usersBySexAndAge.name: 'Käyttäjät sukupuolen ja iän mukaan'
setting.lite_demography.usersBySexAndAge.description: 'Käyttäjät sukupuolen ja iän mukaan'
setting.lite_demography.ageDistribution.name: Ikäjakauma
setting.lite_demography.ageDistribution.description: Ikäjakauma
setting.lite_demography.deviceDistribution.name: 'Jakelu laitteittain'
setting.lite_demography.deviceDistribution.description: 'Jakelu laitteittain'
setting.lite_demography.usersByCountries.name: 'Jakelu maittain'
setting.lite_demography.usersByCountries.description: 'Jakelu maittain'
setting.lite_activity.name: 'Yleiset tilastot Toimintaryhmä'
setting.lite_activity.description: 'Yleiset tilastot Toimintaryhmä'
setting.lite_activity.activityInfo.name: Toimintatiedot
setting.lite_activity.activityInfo.description: 'Portaalissa aktiiviset henkilöt, rekisteröityneet henkilöt, henkilöt, jotka ovat kirjautuneet sisään vähintään kerran viimeisten 30 päivän aikana, deaktivoidut henkilöt ja henkilöt, jotka eivät ole koskaan kirjautuneet sisään'
setting.lite_activity.accessDays.name: 'Pääsy päivien mukaan'
setting.lite_activity.accessDays.description: Pääsypäivät
setting.lite_activity.platformAccessByHours.name: 'Pääsy alustan ja ajan mukaan'
setting.lite_activity.platformAccessByHours.description: 'Laiturille pääsyn ajat päivittäin ja kellonajoittain (lämpökartta)'
setting.lite_activity.courseStartTime.name: 'Jakauma kurssien alkamisajankohtien mukaan'
setting.lite_activity.courseStartTime.description: 'Kurssin alkamisajat'
setting.lite_activity.courseEndTime.name: 'Jakauma kurssin suorittamisen tuntien mukaan'
setting.lite_activity.courseEndTime.description: 'Kurssin suoritustunnit (lämpökartta)'
setting.lite_activity.coursesStartedVsFinished.name: 'Aloitetut kurssit verrattuna suoritettuihin kursseihin'
setting.lite_activity.coursesStartedVsFinished.description: 'Aloitetut kurssit vs. suoritetut kurssit'
setting.lite_activity.usersMoreActivesByActivity.name: 'Aktiivisimmat käyttäjät'
setting.lite_activity.usersMoreActivesByActivity.description: 'Aktiivisimmat ja vähiten aktiiviset ihmiset ja heidän alustalla viettämänsä aika'
setting.lite_itinerary.name: 'Yleisten tilastojen ryhmän matkareitit'
setting.lite_itinerary.description: 'Yleisten tilastojen ryhmän matkareitit'
setting.lite_itinerary.itinerariesStartedAndFinished.name: 'Aloittanut ja suorittanut matkasuunnitelmat'
setting.lite_itinerary.itinerariesStartedAndFinished.description: 'Aloitetut ja toteutetut matkat'
setting.lite_itinerary.itinerariesCompletedByCountries.name: 'Toteutetut matkareitit maittain'
setting.lite_itinerary.itinerariesCompletedByCountries.description: 'Toteutetut matkareitit maittain'
setting.survey.hide_empty_comment.name: 'Piilota mielipide tyhjällä kommentilla'
setting.survey.hide_empty_comment.description: 'Kyselyt, piilota nimi, kun kommentti on tyhjä'
setting.survey.show_only_ratings.name: 'Tutkimus, näytä vain pätevyyden nimi'
setting.survey.show_only_ratings.description: 'Kyselyssä näkyy vain tutkinnon nimi'
app.survey.post_nps.enabled.name: 'Nimi tutkimusjulkaisu nps käytössä'
app.survey.post_nps.enabled.description: 'Tutkimuksen nimi sovelluksen julkaisu nps käytössä'
setting.lite_evolution.segmentedHours.description: Tunnit
setting.course.tab.person.description: 'Ihmiset, kurssin yksityiskohdat'
setting.course.showDeactivatedCourses.name: 'Näytä deaktivoitujen kurssien nimet välilehdellä'
setting.course.showDeactivatedCourses.description: 'Näyttää deaktivoitujen kurssien nimet'
catalog.19.name: 'Käännökset hallintovirkailija'
catalog.19.description: 'Hallinnoijan käännökset'
setting.lenguage.platform: 'Ylläpitäjän käännökset'
setting.module.announcement.name: Hakuilmoitus
setting.module.announcement.description: 'Aktivoi puhelut-moduuli kurssien alivalikossa'
course.diploma.index: Sisällysluettelo
setting.zip.day_available_until.name: 'Käytettävissä olevat päivät'
setting.zip.day_available_until.description: 'Käytettävissä olevien päivien määrä ennen kuin zip poistetaan automaattisesti.'
catalog.20.name: 'Lisäkenttien kutsu'
course.diploma.filters: 'Lisäsuodattimien aktivointi tutkintoraportissa'
setting.lenguage.platform.description: 'Järjestelmänvalvojan paneelissa käytettävissä olevat kielet'
translations_admin.title1: 'Määrätty koulutus'
translations_admin.title2: Lisäkoulutus
translations_admin.title3: 'Osoitetut kurssit'
translations_admin.title4: 'Vapaaehtoiset kurssit'
setting.course.tab.stats.description: 'Käytössä aktivoi "Tilastot"-osion kurssin yksityiskohtaisella tasolla.'
setting.course.tab.options.description: 'Käytössä aktivoi "Palaute"-osion kurssin yksityiskohtaisella tasolla.'
course.diploma.index.description: 'Käytössä "Diplomat"-osio aktivoituu kurssia luotaessa/muuttaessa'
setting.use.filter_in_ranking.name: 'Suodattimien käyttö käyttäjän luokittelussa'
setting.use.filter_in_ranking.description: 'Voit valita valikosta niiden luokkien suodattimet, joita käyttäjä haluaa verrata. Jos tämä vaihtoehto on poistettu käytöstä, käyttäjää verrataan oletusarvoisesti kaikkien alustalla käytettävissä olevien suodattimien kanssa'
setting.use.include_only_first_category_name: 'Näytä vain suodattimen ensimmäinen luokka rankingissa'
setting.use.include_only_first_category_description: 'Jos se on aktiivinen, vain käyttäjän ensimmäinen linkitys näytetään. Muussa tapauksessa kaikki liitetyt luokat näytetään. Jos luokka on esimerkiksi "maa", käyttäjä voi olla linkitetty sekä Espanjaan että Nicaraguaan.'
setting.email_support_error.name: 'Tukiposti virheiden varalta'
setting.email_support_error.description: 'Sähköpostit, joihin alustan häiriötilanteet lähetetään'
setting.export.task.slot_quantity.name: 'Tehtäväpaikkojen määrä käyttäjää kohti'
setting.export.task.slot_quantity.description: 'Vientitehtävien käsittelyyn käytettävissä olevien lähtö- ja saapumisaikojen määrä käyttäjää kohden.'
setting.export.task.long_running_type_tasks.name: 'Pitkäaikaisten tehtävien tyypit'
setting.export.task.long_running_type_tasks.description: 'Luettelo tehtävätyypeistä, joiden katsotaan olevan pitkäkestoisia vientiä varten.'
setting.export.zip_task.slot_quantity.name: 'Vetoketjutehtäväpaikkojen määrä käyttäjää kohti'
setting.export.zip_task.slot_quantity.description: 'Käyttäjäkohtaisten zip-pakkaustehtävien käsittelyyn käytettävissä olevien paikkojen määrä.'
setting.export.zip_task.long_running_type_tasks.name: 'Pitkän aikavälin zip-tehtävätyypit'
setting.export.zip_task.long_running_type_tasks.description: 'Luettelo pitkäkestoisiksi katsotuista zip-tehtävistä.'
setting.export.task.user_pending_max_count_task.name: 'Vireillä olevien tehtävien enimmäismäärä käyttäjää kohti'
setting.export.task.user_pending_max_count_task.description: 'Maksimimäärä vireillä olevia tehtäviä, joita käyttäjällä voi olla jonossa.'
setting.export.task.timeout.name: 'Tehtävien aikaraja'
setting.export.task.timeout.description: 'Enimmäisaika sekunteina ennen kuin vientitehtävä katsotaan vanhentuneeksi.'
setting.export.zip_task.timeout.name: 'Zip-tehtävien aikaraja'
setting.export.zip_task.timeout.description: 'Enimmäisaika sekunteina ennen kuin zip-pakkaustehtävä katsotaan vanhentuneeksi.'
setting.export.task.timeout_seconds.name: 'TIMEOUT-tilassa olevien tehtävien aikakatkaisuaika'
setting.export.task.timeout_seconds.description: 'Enimmäisaika sekunteina, jonka jälkeen TIMEOUT-tilassa olevan tehtävän ei enää katsota olevan käynnissä.'
type_diploma.novomatic.name: Novomatic
type_diploma.novomatic.description: 'Se on Novomaticin henkilökohtainen tutkintotodistus'
app.announcement.managers.sharing.name: 'Mahdollistaa tehtävien luomisen ehdotuspyynnössä'
app.announcement.managers.sharing.description: 'Mahdollistaa tehtävien luomisen ehdotuspyynnössä'
