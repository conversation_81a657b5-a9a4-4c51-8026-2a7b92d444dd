<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Filesystem;

use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Infrastructure\Filesystem\LocalFilesystemFactory;
use League\Flysystem\Filesystem;
use League\Flysystem\UnableToCreateDirectory;
use PHPUnit\Framework\TestCase;

class LocalFilesystemFactoryTest extends TestCase
{
    /**
     * @throws InfrastructureException
     */
    private function getFactory(string $location): Filesystem
    {
        return (new LocalFilesystemFactory())($location);
    }

    /**
     * @throws InfrastructureException
     */
    public function testValidDirectory(): void
    {
        $this->expectNotToPerformAssertions();
        $filesystem = $this->getFactory(
            location: __DIR__
        );
    }
}
