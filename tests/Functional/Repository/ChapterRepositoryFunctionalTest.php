<?php

declare(strict_types=1);

namespace App\Tests\Functional\Repository;

use App\Entity\Chapter;
use App\Entity\ChapterType;
use App\Entity\Course;
use App\Entity\CourseCategory;
use App\Entity\Season;
use App\Tests\Functional\FunctionalTestCase;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\NotSupported;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\Persistence\Mapping\MappingException;

class ChapterRepositoryFunctionalTest extends FunctionalTestCase
{
    private Course $course;
    private Season $season;
    private ChapterType $chapterType;
    private Chapter $activeChapter1;
    private Chapter $activeChapter2;
    private Chapter $inactiveChapter;

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     * @throws NotSupported
     */
    protected function setUp(): void
    {
        parent::setUp();

        $typeCourse = $this->createAndGetTypeCourse();
        $courseCategory = $this->createAndGetCourseCategory();
        $this->course = $this->createAndGetCourse(
            name: 'Test Course for Chapters',
            typeCourse: $typeCourse,
            courseCategory: $courseCategory
        );

        $this->season = $this->createAndGetSeason($this->course);

        $this->chapterType = $this->getEntityManager()
            ->getRepository(ChapterType::class)
            ->find(\App\Enum\ChapterType::CONTENT_TYPE);

        $this->activeChapter1 = $this->createAndGetChapter(
            course: $this->course,
            season: $this->season,
            title: 'Active Chapter 1',
            position: 1,
            active: true,
            chapterType: $this->chapterType
        );

        $this->activeChapter2 = $this->createAndGetChapter(
            course: $this->course,
            season: $this->season,
            title: 'Active Chapter 2',
            position: 2,
            active: true,
            chapterType: $this->chapterType
        );

        $this->inactiveChapter = $this->createAndGetChapter(
            course: $this->course,
            season: $this->season,
            title: 'Inactive Chapter',
            position: 3,
            active: false,
            chapterType: $this->chapterType
        );
    }

    /**
     * @throws NotSupported
     */
    public function testLoadCourseChaptersPaginatedActiveChapters(): void
    {
        $result = $this->getEntityManager()->getRepository(Chapter::class)->loadCourseChaptersPaginated($this->course);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('chapters', $result);
        $this->assertArrayHasKey('totalPages', $result);
        $this->assertArrayHasKey('totalItems', $result);

        // Should have 2 active chapters
        $this->assertCount(2, $result['chapters']);
        $this->assertEquals(1, $result['totalPages']);
        $this->assertEquals(3, $result['totalItems']);

        $chapters = $result['chapters'];
        $this->assertEquals('Active Chapter 1', $chapters[0]->getTitle());
        $this->assertEquals('Active Chapter 2', $chapters[1]->getTitle());
        $this->assertEquals(1, $chapters[0]->getPosition());
        $this->assertEquals(2, $chapters[1]->getPosition());
    }

    /**
     * @throws NotSupported
     */
    public function testLoadCourseChaptersPaginatedWithCustomPagination(): void
    {
        // Test with custom pagination (page 1, limit 1)
        $result = $this->getEntityManager()->getRepository(Chapter::class)->loadCourseChaptersPaginated($this->course, 1, 1);

        $this->assertCount(1, $result['chapters']);
        $this->assertEquals(3, $result['totalPages']);
        $this->assertEquals(3, $result['totalItems']);

        $this->assertEquals('Active Chapter 1', $result['chapters'][0]->getTitle());

        // Test page 2.
        $result = $this->getEntityManager()->getRepository(Chapter::class)->loadCourseChaptersPaginated($this->course, 2, 1);

        $this->assertCount(1, $result['chapters']);
        $this->assertEquals(3, $result['totalPages']);
        $this->assertEquals(3, $result['totalItems']);

        $this->assertEquals('Active Chapter 2', $result['chapters'][0]->getTitle());

        // Test page 3. No chapters return because chapter 3 is inactive.
        $result = $this->getEntityManager()->getRepository(Chapter::class)->loadCourseChaptersPaginated($this->course, 3, 1);

        $this->assertCount(0, $result['chapters']);
        $this->assertEquals(3, $result['totalPages']);
        $this->assertEquals(3, $result['totalItems']);

        $this->assertEmpty($result['chapters']);
    }

    /**
     * @throws NotSupported
     */
    public function testLoadCourseChaptersPaginatedExcludesInactiveChapters(): void
    {
        // Inactive chapters are not included in results.
        $result = $this->getEntityManager()->getRepository(Chapter::class)->loadCourseChaptersPaginated($this->course);

        $chapterTitles = array_map(fn ($chapter) => $chapter->getTitle(), $result['chapters']);

        $this->assertContains('Active Chapter 1', $chapterTitles);
        $this->assertContains('Active Chapter 2', $chapterTitles);
        $this->assertNotContains('Inactive Chapter', $chapterTitles);
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function testLoadCourseChaptersPaginatedWithEmptyCourse(): void
    {
        // Course with no chapters
        $emptyCourse = $this->createAndGetCourse(
            name: 'Empty Course',
            typeCourse: $this->createAndGetTypeCourse(),
            courseCategory: $this->createAndGetCourseCategory()
        );

        $result = $this->getEntityManager()->getRepository(Chapter::class)->loadCourseChaptersPaginated($emptyCourse);

        $this->assertCount(0, $result['chapters']);
        $this->assertEquals(0, $result['totalPages']);
        $this->assertEquals(0, $result['totalItems']);
    }

    /**
     * @throws MappingException
     * @throws Exception
     */
    protected function tearDown(): void
    {
        $this->truncateEntities([
            Chapter::class,
            Season::class,
            Course::class,
            CourseCategory::class,
        ]);

        parent::tearDown();
    }
}
