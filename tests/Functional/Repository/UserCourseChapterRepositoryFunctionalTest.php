<?php

declare(strict_types=1);

namespace App\Tests\Functional\Repository;

use App\Entity\Chapter;
use App\Entity\ChapterType;
use App\Entity\Course;
use App\Entity\CourseCategory;
use App\Entity\Season;
use App\Entity\User;
use App\Entity\UserCourse;
use App\Entity\UserCourseChapter;
use App\Service\Course\DT0\ChapterQueryParams;
use App\Tests\Functional\FunctionalTestCase;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\NotSupported;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\Persistence\Mapping\MappingException;
use PHPUnit\Framework\Attributes\DataProvider;

class UserCourseChapterRepositoryFunctionalTest extends FunctionalTestCase
{
    private Course $course;
    private Chapter $chapter;
    private Season $season;
    private ChapterType $chapterType;
    private User $user1;
    private User $user2;
    private User $user3;
    private UserCourse $userCourse1;
    private UserCourse $userCourse2;
    private UserCourse $userCourse3;
    private UserCourseChapter $userCourse1Chapter;
    private UserCourseChapter $userCourse2Chapter;
    private UserCourseChapter $userCourse3Chapter;

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     * @throws NotSupported
     */
    protected function setUp(): void
    {
        parent::setUp();

        $typeCourse = $this->createAndGetTypeCourse();
        $courseCategory = $this->createAndGetCourseCategory();
        $this->course = $this->createAndGetCourse(
            name: 'Test Course for UserCourseChapter',
            typeCourse: $typeCourse,
            courseCategory: $courseCategory
        );

        $this->season = $this->createAndGetSeason($this->course);

        $this->chapterType = $this->getEntityManager()
            ->getRepository(ChapterType::class)
            ->find(\App\Enum\ChapterType::CONTENT_TYPE);

        $this->chapter = $this->createAndGetChapter(
            course: $this->course,
            season: $this->season,
            title: 'Test Chapter',
            description: 'Test chapter description',
            position: 1,
            active: true,
            chapterType: $this->chapterType
        );

        $this->user1 = $this->createAndGetUser(
            firstName: 'User',
            lastName: 'One',
            email: '<EMAIL>'
        );

        $this->user2 = $this->createAndGetUser(
            firstName: 'User',
            lastName: 'Two',
            email: '<EMAIL>'
        );

        $this->user3 = $this->createAndGetUser(
            firstName: 'User',
            lastName: 'Three',
            email: '<EMAIL>'
        );

        $this->userCourse1 = $this->createAndGetUserCourse(
            user: $this->user1,
            course: $this->course,
            startAt: new \DateTimeImmutable('-10 days'),
            finishedAt: new \DateTimeImmutable('-1 day'),
        );

        $this->userCourse2 = $this->createAndGetUserCourse(
            user: $this->user2,
            course: $this->course,
            startAt: new \DateTimeImmutable('-10 days'),
            finishedAt: new \DateTimeImmutable('-1 day'),
        );

        $this->userCourse3 = $this->createAndGetUserCourse(
            user: $this->user3,
            course: $this->course,
            startAt: new \DateTimeImmutable('-10 days'),
            finishedAt: null,
        );

        $this->userCourse1Chapter = $this->createAndGetUserCourseChapter(
            userCourse: $this->userCourse1,
            chapter: $this->chapter,
            startAt: new \DateTimeImmutable('-10 days'),
            finishedAt: new \DateTimeImmutable('-1 day'),
            timeSpent: 100,
        );

        $this->userCourse2Chapter = $this->createAndGetUserCourseChapter(
            userCourse: $this->userCourse2,
            chapter: $this->chapter,
            startAt: new \DateTimeImmutable('-10 days'),
            finishedAt: new \DateTimeImmutable('-1 day'),
            timeSpent: 200,
        );
        $this->userCourse3Chapter = $this->createAndGetUserCourseChapter(
            userCourse: $this->userCourse3,
            chapter: $this->chapter,
            timeSpent: 300
        );
    }

    /**
     * @throws NotSupported
     */
    public function testGetTimeByChapter(): void
    {
        $chapterQueryParams = new ChapterQueryParams(
            chapter: $this->chapter,
            findUsers: false,
            users: [$this->user1->getId(), $this->user2->getId(), $this->user3->getId()]
        );

        $result = $this->getEntityManager()->getRepository(UserCourseChapter::class)->getTimeByChapter($chapterQueryParams);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('totalTime', $result);

        // User course time spent (100 + 200 + 300)
        $expectedTotalTime = 600;
        $this->assertEquals($expectedTotalTime, $result['totalTime']);
    }

    public function testGetTimeByChapterWithSpecificUsers(): void
    {
        // Test with only finished users
        $chapterQueryParams = new ChapterQueryParams(
            chapter: $this->chapter,
            findUsers: false,
            users: [$this->user1->getId(), $this->user2->getId()]
        );

        $result = $this->getEntityManager()->getRepository(UserCourseChapter::class)->getTimeByChapter($chapterQueryParams);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('totalTime', $result);

        $expectedTotalTime = 300;
        $this->assertEquals($expectedTotalTime, $result['totalTime']);
    }

    public function testGetTotalUserFinishedChapters(): void
    {
        $chapterQueryParams = new ChapterQueryParams(
            chapter: $this->chapter,
            findUsers: false,
            users: [$this->user1->getId(), $this->user2->getId(), $this->user3->getId()]
        );

        $result = $this->getEntityManager()->getRepository(UserCourseChapter::class)->getTotalUserFinishedChapters($chapterQueryParams);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('finished', $result);
        $this->assertArrayHasKey('totalTimeSpent', $result);

        // Should have 2 finished chapters (user1 and user2).
        $this->assertEquals(2, $result['finished']);

        $expectedTotalTimeSpent = 300;
        $this->assertEquals($expectedTotalTimeSpent, $result['totalTimeSpent']);
    }

    /**
     * @throws NotSupported
     */
    public function testChapterGetTotalUsersStarted(): void
    {
        $chapterQueryParams = new ChapterQueryParams(
            chapter: $this->chapter,
            findUsers: false,
            users: [$this->user1->getId(), $this->user2->getId(), $this->user3->getId()]
        );

        $result = $this->getEntityManager()->getRepository(UserCourseChapter::class)->chapterGetTotalUsersStarted($chapterQueryParams);

        // Should return 1 (only user3 is started but not finished).
        $this->assertEquals(1, $result);
    }

    /**
     * @throws OptimisticLockException
     * @throws NotSupported
     * @throws ORMException
     */
    #[DataProvider('findUsersInChapterAndFinishedCourseDataProvider')]
    public function testFindUsersInChapterAndFinishedCourse(array $usersEmails, bool $expected): void
    {
        $users = [];
        foreach ($usersEmails as $email) {
            $user = $this->getEntityManager()->getRepository(User::class)->findOneBy(['email' => $email]);
            $users[] = $user;
            $this->assertNotNull($user);
        }
        // Test with users who have finished the course
        $chapterQueryParams = new ChapterQueryParams(
            chapter: $this->chapter,
            findUsers: false,
            users: $users
        );

        $result = $this->getEntityManager()->getRepository(UserCourseChapter::class)->findUsersInChapterAndFinishedCourse($chapterQueryParams);

        $this->assertEquals($expected, $result);
    }

    public static function findUsersInChapterAndFinishedCourseDataProvider(): \Generator
    {
        yield 'Users in chapter and finished course' => [
            'usersEmails' => [
                '<EMAIL>',
                '<EMAIL>',
            ],
            'expected' => true,
        ];
        yield 'Users in chapter but not all finished course' => [
            'usersEmails' => [
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
            ],
            'expected' => false,
        ];
    }

    /**
     * @throws MappingException
     * @throws Exception
     */
    protected function tearDown(): void
    {
        $this->truncateEntities([
            UserCourseChapter::class,
            UserCourse::class,
            Chapter::class,
            Season::class,
            Course::class,
            CourseCategory::class,
        ]);

        $this->hardDeleteUsersByIds([
            $this->user1->getId(),
            $this->user2->getId(),
            $this->user3->getId(),
        ]);

        parent::tearDown();
    }
}
