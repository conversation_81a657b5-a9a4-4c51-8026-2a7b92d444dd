<?php

declare(strict_types=1);

namespace App\Tests\Functional;

use App\Entity\Setting;
use App\Entity\TypeCourse;
use App\Repository\UserRepository;
use App\Resources\DataFixtureBase\Settings\SettingData;
use App\Service\SettingsService;
use App\Tests\Functional\HelperTrait\AnnouncementHelperTrait;
use App\Tests\Functional\HelperTrait\ChapterHelperTrait;
use App\Tests\Functional\HelperTrait\ChapterTypeHelperTrait;
use App\Tests\Functional\HelperTrait\ContentHelperTrait;
use App\Tests\Functional\HelperTrait\CourseCategoryHelperTrait;
use App\Tests\Functional\HelperTrait\CourseHelperTrait;
use App\Tests\Functional\HelperTrait\ExportHelperTrait;
use App\Tests\Functional\HelperTrait\FilesManagerHelperTrait;
use App\Tests\Functional\HelperTrait\FilterHelperTrait;
use App\Tests\Functional\HelperTrait\HelpCategoryHelperTrait;
use App\Tests\Functional\HelperTrait\OpinionHelperTrait;
use App\Tests\Functional\HelperTrait\SeasonHelperTrait;
use App\Tests\Functional\HelperTrait\SettingHelperTrait;
use App\Tests\Functional\HelperTrait\TaskHelperTrait;
use App\Tests\Functional\HelperTrait\UserHelperTrait;
use App\Tests\Utils\CustomGenerator;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\EntityManager;
use Doctrine\ORM\Exception\NotSupported;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\Mapping\ClassMetadataInfo;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\Persistence\Mapping\MappingException;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

class FunctionalTestCase extends WebTestCase
{
    use UserHelperTrait;
    use CourseHelperTrait;
    use CourseCategoryHelperTrait;
    use HelpCategoryHelperTrait;
    use AnnouncementHelperTrait;
    use OpinionHelperTrait;
    use FilterHelperTrait;
    use TaskHelperTrait;
    use ExportHelperTrait;
    use SettingHelperTrait;
    use SeasonHelperTrait;
    use ChapterHelperTrait;
    use ContentHelperTrait;
    use ChapterTypeHelperTrait;
    use FilesManagerHelperTrait;

    public const array FORBIDDEN_ENTITIES_TO_TRUNCATE = [
        TypeCourse::class,
    ];

    protected const int DEFAULT_USER_ID = 1;
    protected const string DEFAULT_USER_EMAIL = '<EMAIL>';
    protected const string DEFAULT_USER_PASSWORD = '12345678';
    protected const string DEFAULT_USER_LOCALE_CAMPUS = 'es';
    protected const string DEFAULT_USER_LOCALE = 'es';
    protected const string DEFAULT_PLATFORM_LOCALE = 'es';
    protected const array DEFAULT_PLATFORM_LOCALES = ['es', 'en', 'fr', 'pt'];
    protected const array DEFAULT_ADMIN_ROLES = ['ROLE_USER', 'ROLE_ADMIN'];
    protected const string DEFAULT_TEST_IP = '*******';
    protected static bool $debugMode = false;

    /** @var bool Make sure the basic data initialization is executed only once in the tests lifecycle */
    private static bool $isDataInitialized = false;

    protected ?KernelBrowser $client;

    protected function log(string $message): void
    {
        if (!self::$debugMode) {
            return;
        }

        fwrite(STDOUT, $message . PHP_EOL);
    }

    protected function setUp(): void
    {
        parent::setUp();

        try {
            $this->client = self::createClient();

            $this->client->setServerParameter('REMOTE_ADDR', self::DEFAULT_TEST_IP);

            if (false === self::$isDataInitialized) {
                $this->restoreDefaultUserState();

                $this->updateSettingsInDatabase([
                    'app.languages' => self::DEFAULT_PLATFORM_LOCALES,
                    'app.defaultLanguage' => self::DEFAULT_PLATFORM_LOCALE,
                ]);
                self::$isDataInitialized = true;
            }
        } catch (\Exception $e) {
            $this->fail('You cannot create the client used in functional tests. Check the error: ' . $e->getMessage());
        }

        $this->client->disableReboot();
    }

    /**
     * @throws MappingException
     */
    protected function tearDown(): void
    {
        $this->restoreDefaultUserState(true);
        $this->getEntityManager()->clear();

        parent::tearDown();

        $this->client = null;

        restore_exception_handler();
    }

    /**
     * Restores the default state of the test user.
     */
    private function restoreDefaultUserState(bool $onlyLocales = false): void
    {
        $defaultUser = $this->getDefaultUser();

        if (!$onlyLocales) {
            /** @var UserPasswordHasherInterface $passwordHasher */
            $passwordHasher = $this->client
                ->getContainer()
                ->get('Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface');

            /* @var UserRepository $userRepository */
            $defaultUser->setPassword($passwordHasher->hashPassword($defaultUser, self::DEFAULT_USER_PASSWORD));
            $defaultUser->setEmail(self::DEFAULT_USER_EMAIL);
        }

        $defaultUser->setLocaleCampus(self::DEFAULT_USER_LOCALE_CAMPUS);
        $defaultUser->setLocale(self::DEFAULT_USER_LOCALE);
        $defaultUser->setRoles(self::DEFAULT_ADMIN_ROLES);

        $this->getEntityManager()->flush();
    }

    /**
     * @throws Exception
     */
    protected function truncateEntities(array $items): void
    {
        $intercept = array_intersect(self::FORBIDDEN_ENTITIES_TO_TRUNCATE, $items);
        if (\count($intercept) > 0) {
            $this->fail('One or more entities are not allowed to be truncated.');
        }

        $entityManager = $this->getEntityManager();
        $connection = $entityManager->getConnection();
        $connection->executeStatement('SET FOREIGN_KEY_CHECKS=0;');
        $databasePlatform = $connection->getDatabasePlatform();

        foreach ($items as $item) {
            $tableName = null;

            if (class_exists($item)) {
                $tableName = $entityManager->getClassMetadata($item)->getTableName();
            } else {
                $tableName = $item;
            }

            $connection->executeStatement(
                $databasePlatform->getTruncateTableSQL($tableName)
            );
        }
        $connection->executeStatement('SET FOREIGN_KEY_CHECKS=1;');
    }

    /**
     * @param string|array $body
     */
    protected function makeRequest(
        string $method,
        string $uri,
        $body = [],
        array $queryParams = [],
        array $headers = [],
        ?array $files = null,
        ?string $bearerToken = null
    ): Response {
        $headers = array_merge(
            $bearerToken ? ['HTTP_Authorization' => 'Bearer ' . $bearerToken] : [],
            $headers
        );

        $this->client->request(
            $method,
            'http://localhost' . $uri,
            $queryParams,
            $files ?? [],
            $headers,
            \is_array($body) ? json_encode($body) : $body,
        );

        return $this->client->getResponse();
    }

    protected function makeFrontendApiRequest(
        string $method,
        string $uri,
        $body = [],
        array $queryParams = [],
        array $headers = [],
        ?string $bearerToken = null
    ): Response {
        $uri = '/api' . $uri;

        return $this->makeRequest(method: $method, uri: $uri, body: $body, queryParams: $queryParams, headers: $headers, bearerToken: $bearerToken);
    }

    protected function makeAdminApiRequest(
        string $method,
        string $uri,
        $body = [],
        array $queryParams = [],
        array $headers = [],
        ?string $bearerToken = null
    ): Response {
        $uri = '/admin' . $uri;

        return $this->makeRequest(method: $method, uri: $uri, body: $body, queryParams: $queryParams, headers: $headers, bearerToken: $bearerToken);
    }

    protected function assertManagedErrorResponse(Response $response, int $responseCode, $message): void
    {
        $this->assertEquals($responseCode, $response->getStatusCode());
        $this->assertJson($response->getContent());

        $responseDecode = json_decode($response->getContent(), true);

        $this->assertArrayHasKey('error', $responseDecode);
        $this->assertTrue($responseDecode['error']);
        $this->assertArrayHasKey('data', $responseDecode);

        if (\is_array($responseDecode['data']) && \is_array($message)) {
            $this->assertEquals($message, $responseDecode['data']);
        } elseif (\is_array($responseDecode['data']) && \is_string($message)) {
            $this->assertEquals($message, implode("\n", $responseDecode['data']));
        } elseif (\is_string($responseDecode['data']) && \is_string($message)) {
            $this->assertEquals($message, $responseDecode['data']);
        } else {
            $this->fail('The data type of the response does not match the expected type.');
        }
    }

    /**
     * @return array|string
     */
    protected function extractResponseData(Response $response)
    {
        $this->assertJson($response->getContent());

        $responseDecode = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('data', $responseDecode);

        return $responseDecode['data'];
    }

    protected function getEntityManager(): EntityManager
    {
        return $this->client->getContainer()->get('doctrine')->getManager();
    }

    protected function getService(string $className): object
    {
        return $this->client->getContainer()->get($className);
    }

    protected function getTranslator(): object
    {
        return $this->getService(TranslatorInterface::class);
    }

    /**
     * Retrieves a translated message for a given locale.
     *
     * This method fetches the translated string for a given message key from the translator,
     * using the specified domain and locale. If parameters are provided, they are formatted into the message.
     */
    protected function getTranslatedMessage(
        string $messageKey,
        array $parameters = [],
        string $domain,
        string $locale
    ): string {
        $translatedMessage = $this->getTranslator()->trans($messageKey, [], $domain, $locale);

        if (!empty($parameters)) {
            if (isset($parameters[0]) && \is_array($parameters[0]) && $this->isAssociative($parameters[0])) {
                return $this->getTranslator()->trans($messageKey, $parameters[0], $domain, $locale);
            }

            if ($this->isAssociative($parameters)) {
                return $this->getTranslator()->trans($messageKey, $parameters, $domain, $locale);
            }

            return \sprintf($translatedMessage, ...$parameters);
        }

        return $translatedMessage;
    }

    private function isAssociative(array $arr): bool
    {
        if ([] === $arr) {
            return false;
        }

        return array_keys($arr) !== range(0, \count($arr) - 1);
    }

    /**
     * Asserts that the translated message matches the expected value in the response data.
     *
     * This method checks if the expected translated message appears in the response data.
     * If the expected message is of type "translation", it retrieves the translated string
     * using `getTranslatedMessage`. Otherwise, it compares the direct message.
     */
    protected function assertTranslatedMessage($responseData, array $expectedMessage, string $locale): void
    {
        $translatedMessage = (isset($expectedMessage['type']) && 'translation' === $expectedMessage['type'])
            ? $this->getTranslatedMessage(
                $expectedMessage['key'],
                isset($expectedMessage['params']) ? $expectedMessage['params'] : [],
                $expectedMessage['origin'],
                $locale
            )
            : $expectedMessage['message'];

        if (\is_array($responseData['data'] ?? null)) {
            $this->assertContains(
                $translatedMessage,
                $responseData['data'],
                "[Locale: $locale] Translated Message mismatch. Expected message '$translatedMessage' not found in response array."
            );
        } else {
            $this->assertEquals(
                $translatedMessage,
                $responseData['data'],
                "[Locale: $locale] Translated Message mismatch. Expected '$translatedMessage' in response data."
            );
        }
    }

    /**
     * @throws NotSupported
     */
    protected function getRepository(string $entityClass): \Doctrine\ORM\EntityRepository
    {
        return $this->getEntityManager()->getRepository($entityClass);
    }

    /**
     * @throws OptimisticLockException
     * @throws NotSupported
     * @throws ORMException
     */
    protected function updateSettingsInDatabase(array $settings): void
    {
        $entityManager = $this->getEntityManager();
        $settingRepository = $entityManager->getRepository(Setting::class);

        foreach ($settings as $code => $value) {
            $setting = $settingRepository->findOneBy(['code' => $code]);
            if ($setting) {
                $setting->setValue(\is_array($value) ? json_encode($value) : $value);
            }
        }

        $entityManager->flush();
    }

    protected static function getSettingValue(string $settingGroupCode, string $settingCode)
    {
        foreach (SettingData::DEFAULT_DATA as $settingGroup) {
            if ($settingGroupCode === $settingGroup['code']) {
                foreach ($settingGroup['settings'] as $setting) {
                    if ($settingCode === $setting['code']) {
                        return $setting['value'] ?: '';
                    }
                }
            }
        }

        return '';
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    protected function updateSettingValue(string $value, string $code): void
    {
        $settingsService = $this->client->getContainer()->get(SettingsService::class);

        $settingsService->setSetting($code, $value);
    }

    protected function setLocaleForUser(string $locale): void
    {
        $user = $this->getDefaultUser();
        $user->setLocaleCampus($locale);
        $user->setLocale($locale);

        $entityManager = $this->getEntityManager();
        $entityManager->persist($user);
        $entityManager->flush();
    }

    /**
     * Adds locale variations to test cases.
     *
     * If a test case contains a translatable message, it is duplicated for each default platform locale.
     * Otherwise, it is assigned the default locale.
     *
     *  * ### Expected Input Structure (`$yields` parameter)
     *   The input should be an associative array where:
     * - **Key:** A descriptive name for the test case.
     * - **Value:** should contain an array:
     *   - `expectedMessage` (optional): An associative array that may contain:
     *     - `type`: Can be `"translation"` or `"direct"`.
     *     - `key`: The translation key (if applicable).
     *     - `origin`: Source of the translation message.
     *     - `params`: Any additional parameters for translation.
     *   - `expectedMessages` (optional): An array of multiple messages, each structured like `expectedMessage`.
     */
    protected static function addLocalesToYields(array $yields): \Generator
    {
        foreach ($yields as $name => $testCase) {
            $hasTranslation = false;

            if (isset($testCase['expectedMessage']['type']) && 'translation' === $testCase['expectedMessage']['type']) {
                $hasTranslation = true;
            }

            if (isset($testCase['expectedMessages'])) {
                foreach ($testCase['expectedMessages'] as $msg) {
                    if ('translation' === $msg['type']) {
                        $hasTranslation = true;
                        break;
                    }
                }
            }

            if ($hasTranslation) {
                foreach (self::DEFAULT_PLATFORM_LOCALES as $locale) {
                    $translatedTestCase = $testCase;
                    $translatedTestCase['locale'] = $locale;
                    yield "$name [$locale]" => $translatedTestCase;
                }
            } else {
                $translatedTestCase = $testCase;
                $translatedTestCase['locale'] = self::DEFAULT_PLATFORM_LOCALE;
                yield $name => $translatedTestCase;
            }
        }
    }

    /**
     * Provides available platform locales for testing.
     *
     *  * This structure allows PHPUnit to execute the same test multiple times, once for each locale.
     */
    public static function localeProvider(): array
    {
        return array_map(fn ($locale) => [$locale], self::DEFAULT_PLATFORM_LOCALES);
    }

    /**
     * Temporarily activates the DeletedByListener in the test environment.
     */
    protected function activateDeletedByListener(): void
    {
        // Manually create an instance of DeletedByListener
        $logger = $this->client->getContainer()->get('logger');
        $tokenStorage = $this->client->getContainer()->get(TokenStorageInterface::class);
        $deletedByListener = new \App\EntityListener\DeletedByListener($tokenStorage, $logger);

        // Get Doctrine's EventManager
        $eventManager = $this->getEntityManager()->getEventManager();

        // Register the listener for the preSoftDelete event
        $eventManager->addEventListener('preSoftDelete', $deletedByListener);

        $this->log('DeletedByListener temporarily activated');
    }

    /**
     * Deactivates the DeletedByListener in the test environment.
     */
    protected function deactivateDeletedByListener(): void
    {
        // Get Doctrine's EventManager
        $eventManager = $this->getEntityManager()->getEventManager();

        // Remove all listeners for the preSoftDelete event
        $listeners = $eventManager->getListeners('preSoftDelete');
        foreach ($listeners as $listener) {
            if ($listener instanceof \App\EntityListener\DeletedByListener) {
                $eventManager->removeEventListener('preSoftDelete', $listener);
            }
        }

        $this->log('DeletedByListener deactivated');
    }

    /**
     * If save is set to false and the entity class will be further utilized for new storage and no custom ids,
     * remember to restore default metadata state.
     */
    protected function setCustomIdToEntity($entity)
    {
        $em = $this->getEntityManager();
        $metadata = $em->getClassMetadata(\get_class($entity));
        $originalMetadata = clone $metadata;

        if (CustomGenerator::isIdAssigned($em, $entity)) {
            $metadata->generatorType = ClassMetadataInfo::GENERATOR_TYPE_CUSTOM;
            $metadata->idGenerator = new CustomGenerator($metadata->idGenerator);
        }

        return $originalMetadata;
    }

    protected function restoreEntityMetadata($entity, $originalMetadata): void
    {
        $em = $this->getEntityManager();
        $metadata = $em->getClassMetadata(\get_class($entity));
        $metadata->generatorType = $originalMetadata->generatorType;
        $metadata->idGenerator = $originalMetadata->idGenerator;
    }
}
