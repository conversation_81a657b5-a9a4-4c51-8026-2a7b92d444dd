{% extends 'base.html.twig' %}

{% block title %}Challenge index{% endblock %}

{% block body %}
    <h1>{{ 'messages.challengeindex'|trans({}, 'translations', announcementUser.user.locale) }}</h1>

    <table class="table">
        <thead>
            <tr>
                <th>Id</th>
                <th>{{ 'chapter.configureFields.title'|trans({}, 'messages', announcementUser.user.locale) }}</th>
                <th>{{ 'taskCourse.configureFields.startDate'|trans({}, 'messages', announcementUser.user.locale) }}</th>
                <th>{{ 'stats.export.end_date'|trans({}, 'messages', announcementUser.user.locale) }}</th>
                <th>{{ 'material_course.configureFields.type_4'|trans({}, 'messages', announcementUser.user.locale) }}</th>
                <th>{{ 'common_areas.actions'|trans({}, 'messages', announcementUser.user.locale) }}</th>
            </tr>
        </thead>
        <tbody>
        {% for challenge in challenges %}
            <tr>
                <td>{{ challenge.id }}</td>
                <td>{{ challenge.title }}</td>
                <td>{{ challenge.startDate ? challenge.startDate|date('Y-m-d H:i:s') : '' }}</td>
                <td>{{ challenge.endDate ? challenge.endDate|date('Y-m-d H:i:s') : '' }}</td>
                <td>{{ challenge.image }}</td>
                <td>
                    <a href="{{ path('challenge_show', {'id': challenge.id}) }}">{{ 'Show'|trans({}, 'messages', announcementUser.user.locale) }}</a>
                    <a href="{{ path('challenge_edit', {'id': challenge.id}) }}">{{ 'Edit'|trans({}, 'messages', announcementUser.user.locale) }}</a>
                </td>
            </tr>
        {% else %}
            <tr>
                <td colspan="6">{{ 'no_result'|trans({}, 'messages', announcementUser.user.locale) }}</td>
            </tr>
        {% endfor %}
        </tbody>
    </table>

    <a href="{{ path('challenge_new') }}">{{ 'messages.createnew'|trans({}, 'translations', announcementUser.user.locale) }}</a>
{% endblock %}
