{% extends 'base.html.twig' %}

{% block title %}Active{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    {{ encore_entry_link_tags('app') }}
{% endblock %}

{% block body %}
<div id="login" class="activeAccount">
<div class="contentAccount">
   {{ include('reset_password/header.html.twig') }}
   {% if recoveryCode.state == false %}
   <div class="groupButton">
        <form method="POST" action="{{path('change-password')}}" class="col-md-12">
            <input type="hidden" value="{{user.id}}" name="id">
            <input type="hidden" value="{{hash}}" name="hash" class="form-control">
            <div>
            <label>{{ 'email.reset_password.write_password'|trans({}, 'email', user.locale) }}</label>
            <input type="password" name="password" class="form-control" required>
            </div>
            <button  class="button buttonWhite btn-block" type="submit">{{ 'email.template_email.change-password'|trans({}, 'email', user.locale) }} </button>
        </form>
    </div>
    {% else %}
    <div> <h4 class="text-center">{{ 'email.active_account.page_nof_found'|trans({}, 'email', user.locale) }}</h4></div>
    {% endif %}
</div>
</div>

{% endblock %}
