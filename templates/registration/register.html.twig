{% extends 'base.html.twig' %}

{% block title %}Register{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    {{ encore_entry_link_tags('app') }}
{% endblock %}

{% block body %}
 

<div id="login" class="container h-100">  
    {% for flashError in app.flashes('verify_email_error') %}
        <div class="alert alert-danger" role="alert">{{ flashError }}</div>
    {% endfor %}
    <div class="titleForm">
        <h1 class="h3 mb-3 font-weight-normal"> {{ 'security.register'|trans({}, 'messages') }}</h1>
    </div>
   

    {{ form_start(registrationForm) }}
        {{ form_row(registrationForm.email) }}
        {{ form_row(registrationForm.first_name) }}
            {{ form_row(registrationForm.last_name) }}
        {{ form_row(registrationForm.password.first, {
            label:    'security.password'|trans({}, 'messages') 
        }) }}    
         {{ form_row(registrationForm.password.second, {
            label:  'security.repeat_password'|trans({}, 'messages')
        }) }}  
        <div class="checkBoxRegister">         
        {{ form_row(registrationForm.agreeTerms) }}
        </div>
    <div class="groupButton">
        <button type="submit" class="button buttonPrimary"> {{ 'security.button_register'|trans({}, 'messages') }}</button>
        <a class="button buttonWhite btn-block"  href="{{path('app_login')}}">
          {{ 'security.button_exist_accoutn'|trans({}, 'messages') }}
        </a>
    </div>
    {{ form_end(registrationForm) }}

    </div> 
{% endblock %}
